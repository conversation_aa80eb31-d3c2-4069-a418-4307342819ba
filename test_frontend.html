<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Test - BadBoyz IPTV</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { padding: 10px; margin: 5px 0; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <h2>🧪 Frontend JavaScript Test</h2>
    <p>This page tests if the JavaScript functions work without errors.</p>
    
    <div id="test-results"></div>
    
    <script>
        const results = document.getElementById('test-results');
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }
        
        // Test 1: Check if main elements exist (simulate reseller-panel structure)
        addResult('🔍 Testing DOM element access...', 'info');
        
        // Create mock elements that the JavaScript expects
        const mockElements = [
            { id: 'loginScreen', tag: 'div' },
            { id: 'dashboard', tag: 'div' },
            { id: 'loginForm', tag: 'form' },
            { id: 'username', tag: 'input' },
            { id: 'password', tag: 'input' },
            { id: 'userName', tag: 'span' },
            { id: 'customersTableBody', tag: 'tbody' },
            { id: 'registerUserForm', tag: 'form' },
            { id: 'loadingSpinner', tag: 'div' }
        ];
        
        mockElements.forEach(elem => {
            const element = document.createElement(elem.tag);
            element.id = elem.id;
            if (elem.id === 'dashboard') element.classList.add('hidden');
            document.body.appendChild(element);
        });
        
        // Add some stat-number elements
        for (let i = 0; i < 4; i++) {
            const statDiv = document.createElement('div');
            statDiv.className = 'stat-number';
            statDiv.textContent = '0';
            document.body.appendChild(statDiv);
        }
        
        addResult('✅ Mock DOM elements created', 'success');
        
        // Test 2: Load the JavaScript file
        try {
            const script = document.createElement('script');
            script.src = 'reseller-script-api.js';
            script.onload = function() {
                addResult('✅ JavaScript file loaded successfully', 'success');
                
                // Test 3: Check if functions exist
                const functionsToTest = [
                    'logout',
                    'showAddCustomerModal', 
                    'closeModal',
                    'showTab'
                ];
                
                functionsToTest.forEach(funcName => {
                    if (typeof window[funcName] === 'function') {
                        addResult(`✅ Function '${funcName}' exists`, 'success');
                    } else {
                        addResult(`❌ Function '${funcName}' missing`, 'error');
                    }
                });
                
                // Test 4: Try calling some functions
                try {
                    showTab('dashboard');
                    addResult('✅ showTab() function works', 'success');
                } catch (e) {
                    addResult(`❌ showTab() error: ${e.message}`, 'error');
                }
                
                try {
                    closeModal('testModal');
                    addResult('✅ closeModal() function works', 'success');
                } catch (e) {
                    addResult(`❌ closeModal() error: ${e.message}`, 'error');
                }
                
                addResult('🎉 Frontend test completed! Check results above.', 'info');
                addResult('📝 If you see mostly ✅ green checkmarks, the JavaScript fixes are working!', 'info');
                addResult('🔒 Delete this test file after checking.', 'info');
            };
            
            script.onerror = function() {
                addResult('❌ Failed to load JavaScript file', 'error');
                addResult('Make sure reseller-script-api.js exists in the same directory', 'error');
            };
            
            document.head.appendChild(script);
            
        } catch (e) {
            addResult(`❌ Error loading script: ${e.message}`, 'error');
        }
    </script>
</body>
</html>
