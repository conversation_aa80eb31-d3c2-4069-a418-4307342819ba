<?php
/**
 * Authentication API Endpoints
 */

function handleAuthRequest($method, $pathParts, $input) {
    switch ($method) {
        case 'POST':
            if (isset($pathParts[1])) {
                switch ($pathParts[1]) {
                    case 'login':
                        handleLogin($input);
                        break;
                    case 'logout':
                        handleLogout();
                        break;
                    case 'refresh':
                        handleRefreshToken();
                        break;
                    default:
                        errorResponse('Invalid auth endpoint', 404);
                }
            } else {
                errorResponse('Auth action required', 400);
            }
            break;
            
        case 'GET':
            if (isset($pathParts[1]) && $pathParts[1] === 'verify') {
                handleVerifyToken();
            } else {
                errorResponse('Invalid auth endpoint', 404);
            }
            break;
            
        default:
            errorResponse('Method not allowed', 405);
    }
}

function handleLogin($input) {
    validateRequired($input, ['username', 'password']);
    
    $username = sanitizeInput($input['username']);
    $password = $input['password'];
    
    $db = getDB();
    
    try {
        // Check for account lockout
        $stmt = $db->prepare("
            SELECT id, username, password_hash, full_name, role, status, 
                   login_attempts, locked_until 
            FROM reseller_admins 
            WHERE username = ?
        ");
        $stmt->execute([$username]);
        $admin = $stmt->fetch();
        
        if (!$admin) {
            // Log failed attempt
            logError("Login attempt with invalid username: $username");
            errorResponse('Invalid credentials', 401);
        }
        
        // Check if account is locked
        if ($admin['locked_until'] && strtotime($admin['locked_until']) > time()) {
            $lockTime = date('Y-m-d H:i:s', strtotime($admin['locked_until']));
            errorResponse("Account locked until $lockTime", 423);
        }
        
        // Check if account is active
        if ($admin['status'] !== 'active') {
            errorResponse('Account is inactive', 403);
        }
        
        // Verify password
        if (!verifyPassword($password, $admin['password_hash'])) {
            // Increment login attempts
            $attempts = $admin['login_attempts'] + 1;
            $lockUntil = null;
            
            if ($attempts >= 5) {
                // Lock account for 30 minutes
                $lockUntil = date('Y-m-d H:i:s', time() + 1800);
            }
            
            $stmt = $db->prepare("
                UPDATE reseller_admins 
                SET login_attempts = ?, locked_until = ? 
                WHERE id = ?
            ");
            $stmt->execute([$attempts, $lockUntil, $admin['id']]);
            
            logError("Failed login attempt for user: $username (Attempt $attempts)");
            errorResponse('Invalid credentials', 401);
        }
        
        // Successful login - reset attempts and update last login
        $token = generateToken();
        
        $stmt = $db->prepare("
            UPDATE reseller_admins 
            SET login_attempts = 0, locked_until = NULL, last_login = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$admin['id']]);
        
        // Start session
        $_SESSION['admin_id'] = $admin['id'];
        $_SESSION['username'] = $admin['username'];
        $_SESSION['role'] = $admin['role'];
        $_SESSION['token'] = $token;
        $_SESSION['login_time'] = time();

        // Also store token in database for verification
        $stmt = $db->prepare("
            UPDATE reseller_admins
            SET last_login = NOW(), auth_token = ?, token_expires = DATE_ADD(NOW(), INTERVAL 24 HOUR)
            WHERE id = ?
        ");
        $stmt->execute([$token, $admin['id']]);
        
        logError("Successful login for user: $username");
        
        successResponse([
            'token' => $token,
            'user' => [
                'id' => (int)$admin['id'],
                'username' => $admin['username'],
                'full_name' => $admin['full_name'],
                'role' => $admin['role']
            ],
            'expires_in' => SESSION_TIMEOUT
        ]);
        
    } catch (Exception $e) {
        logError("Login error: " . $e->getMessage());
        errorResponse('Login failed', 500);
    }
}

function handleLogout() {
    session_start();
    
    if (isset($_SESSION['admin_id'])) {
        $username = $_SESSION['username'] ?? 'unknown';
        logError("User logged out: $username");
    }
    
    // Destroy session
    session_destroy();
    
    successResponse(['message' => 'Logged out successfully']);
}

function handleVerifyToken() {
    $adminId = verifyAuth();
    
    $db = getDB();
    $stmt = $db->prepare("
        SELECT id, username, full_name, role, status 
        FROM reseller_admins 
        WHERE id = ?
    ");
    $stmt->execute([$adminId]);
    $admin = $stmt->fetch();
    
    if (!$admin || $admin['status'] !== 'active') {
        errorResponse('Invalid token', 401);
    }
    
    successResponse([
        'user' => [
            'id' => (int)$admin['id'],
            'username' => $admin['username'],
            'full_name' => $admin['full_name'],
            'role' => $admin['role']
        ],
        'session_time' => time() - $_SESSION['login_time']
    ]);
}

function handleRefreshToken() {
    $adminId = verifyAuth();
    
    // Generate new token
    $newToken = generateToken();
    $_SESSION['token'] = $newToken;
    
    successResponse([
        'token' => $newToken,
        'expires_in' => SESSION_TIMEOUT
    ]);
}

// Helper function to check if user is authenticated (for other endpoints)
function requireAuth() {
    return verifyAuth();
}

// Helper function to check if user has specific role
function requireRole($requiredRole) {
    $adminId = verifyAuth();
    
    if ($_SESSION['role'] !== $requiredRole && $_SESSION['role'] !== 'admin') {
        errorResponse('Insufficient permissions', 403);
    }
    
    return $adminId;
}
?>
