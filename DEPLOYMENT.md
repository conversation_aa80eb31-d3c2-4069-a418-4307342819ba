# BadBoyz IPTV Website - Deployment Guide

This guide covers various deployment options for the BadBoyz IPTV website.

## Quick Deployment Options

### 1. Netlify (Recommended - Free)

**Method 1: Drag & Drop**
1. Visit [netlify.com](https://netlify.com)
2. Sign up for a free account
3. Drag the entire project folder to the deployment area
4. Your site will be live instantly with a random URL
5. Optional: Configure custom domain in site settings

**Method 2: Git Integration**
1. Push your code to GitHub/GitLab
2. Connect your repository to Netlify
3. Set build settings (not needed for this static site)
4. Deploy automatically on every push

### 2. Vercel (Free)

1. Visit [vercel.com](https://vercel.com)
2. Sign up and connect your GitHub account
3. Import your repository
4. Deploy with zero configuration
5. Get automatic HTTPS and global CDN

### 3. GitHub Pages (Free)

1. Push code to a GitHub repository
2. Go to repository Settings > Pages
3. Select source branch (usually `main`)
4. Your site will be available at `username.github.io/repository-name`

### 4. Firebase Hosting (Free tier available)

```bash
# Install Firebase CLI
npm install -g firebase-tools

# Login to Firebase
firebase login

# Initialize project
firebase init hosting

# Deploy
firebase deploy
```

## Traditional Web Hosting

### Shared Hosting (cPanel, etc.)

1. **Upload Files**
   - Use FTP/SFTP client (FileZilla, WinSCP)
   - Upload all files to `public_html` or `www` directory
   - Maintain file structure

2. **File Permissions**
   - HTML/CSS/JS files: 644
   - Directories: 755

3. **Domain Configuration**
   - Point domain to hosting directory
   - Ensure `index.html` is recognized as default page

### VPS/Dedicated Server

#### Nginx Configuration

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    root /var/www/badboyz-iptv;
    index index.html;

    # Gzip compression
    gzip on;
    gzip_types text/css application/javascript text/javascript;

    # Cache static assets
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    location / {
        try_files $uri $uri/ =404;
    }
}
```

#### Apache Configuration (.htaccess)

```apache
# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache static files
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/ico "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>

# Security headers
Header always set X-Frame-Options SAMEORIGIN
Header always set X-Content-Type-Options nosniff
Header always set Referrer-Policy "no-referrer-when-downgrade"

# Force HTTPS (optional)
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

## CDN Integration

### Cloudflare (Recommended)

1. **Setup**
   - Add your domain to Cloudflare
   - Update nameservers at your domain registrar
   - Enable proxy (orange cloud) for your domain

2. **Optimization Settings**
   - Enable Auto Minify for CSS, JS, HTML
   - Enable Brotli compression
   - Set Browser Cache TTL to 1 year for static assets
   - Enable "Always Online" for better uptime

3. **Security Settings**
   - Enable "Under Attack Mode" if needed
   - Configure firewall rules
   - Enable Bot Fight Mode

### AWS CloudFront

1. Create CloudFront distribution
2. Set origin to your hosting provider
3. Configure caching behaviors
4. Enable compression
5. Set up custom domain with SSL certificate

## Performance Optimization

### Pre-deployment Checklist

- [ ] Minify CSS and JavaScript (optional - files are already optimized)
- [ ] Optimize images (add WebP format if using images)
- [ ] Enable gzip/brotli compression
- [ ] Set proper cache headers
- [ ] Configure CDN
- [ ] Test page speed with Google PageSpeed Insights

### Image Optimization (if adding images later)

```bash
# Install imagemin-cli
npm install -g imagemin-cli imagemin-webp

# Convert images to WebP
imagemin images/*.{jpg,png} --out-dir=images --plugin=webp
```

## SSL Certificate Setup

### Let's Encrypt (Free)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### Cloudflare SSL

1. Enable "Full (strict)" SSL mode
2. Use Cloudflare's Universal SSL certificate
3. Enable "Always Use HTTPS"
4. Enable HSTS (HTTP Strict Transport Security)

## Monitoring and Analytics

### Google Analytics Setup

1. Create Google Analytics account
2. Add tracking code to `index.html` before `</head>`:

```html
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_MEASUREMENT_ID');
</script>


### Uptime Monitoring

- **UptimeRobot** (free): Monitor website availability
- **Pingdom**: Advanced monitoring with performance insights
- **StatusCake**: Free tier available

## Domain Configuration

### DNS Settings

```
Type    Name    Value               TTL
A       @       YOUR_SERVER_IP      300
A       www     YOUR_SERVER_IP      300
CNAME   www     yourdomain.com      300
```

### Subdomain Setup (optional)

```
Type    Name        Value               TTL
A       iptv        YOUR_SERVER_IP      300
CNAME   badboyz     yourdomain.com      300
```

## Backup Strategy

### Automated Backups

```bash
#!/bin/bash
# backup-website.sh
DATE=$(date +%Y%m%d_%H%M%S)
tar -czf "badboyz-iptv-backup-$DATE.tar.gz" /var/www/badboyz-iptv/
aws s3 cp "badboyz-iptv-backup-$DATE.tar.gz" s3://your-backup-bucket/
```

### Version Control

- Keep code in Git repository
- Tag releases: `git tag -a v1.0 -m "Initial release"`
- Use branches for development: `git checkout -b feature/new-feature`

## Troubleshooting

### Common Issues

1. **Files not loading**
   - Check file permissions (644 for files, 755 for directories)
   - Verify file paths are correct
   - Check server error logs

2. **CSS/JS not working**
   - Verify MIME types are configured correctly
   - Check for console errors in browser
   - Ensure files are not blocked by ad blockers

3. **Mobile responsiveness issues**
   - Test with browser dev tools
   - Check viewport meta tag is present
   - Verify CSS media queries

4. **Slow loading**
   - Enable compression (gzip/brotli)
   - Optimize images
   - Use CDN
   - Check server response times

### Testing After Deployment

1. **Functionality Tests**
   - All navigation links work
   - Live chat opens and functions
   - Telegram redirect works
   - Pricing plan selection works
   - Mobile menu toggles correctly

2. **Performance Tests**
   - Google PageSpeed Insights
   - GTmetrix
   - WebPageTest
   - Lighthouse audit

3. **Cross-browser Testing**
   - Chrome, Firefox, Safari, Edge
   - Mobile browsers (iOS Safari, Chrome Mobile)
   - Test on different devices and screen sizes

## Security Considerations

### Basic Security Headers

```
X-Frame-Options: SAMEORIGIN
X-Content-Type-Options: nosniff
Referrer-Policy: no-referrer-when-downgrade
Content-Security-Policy: default-src 'self' 'unsafe-inline' cdnjs.cloudflare.com fonts.googleapis.com fonts.gstatic.com
```

### Regular Maintenance

- Keep hosting platform updated
- Monitor for broken links
- Check SSL certificate expiration
- Review analytics for unusual traffic patterns
- Update external CDN links periodically

## Cost Estimates

### Free Options
- **Netlify**: Free tier (100GB bandwidth/month)
- **Vercel**: Free tier (100GB bandwidth/month)
- **GitHub Pages**: Free (1GB storage, 100GB bandwidth/month)
- **Firebase Hosting**: Free tier (1GB storage, 10GB transfer/month)

### Paid Options
- **Shared Hosting**: $3-10/month
- **VPS**: $5-20/month
- **Dedicated Server**: $50+/month
- **CDN**: $0.01-0.10 per GB

Choose based on expected traffic and performance requirements.
