<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Resizer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            text-align: center;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 10px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .preview {
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Image Resizer for BadBoyz IPTV Logo</h1>
        <input type="file" id="imageInput" accept="image/*">
        <div class="preview">
            <h3>Original Image:</h3>
            <canvas id="originalCanvas"></canvas>
            <h3>Resized Images:</h3>
            <div>
                <p>48x48 pixels (Recommended for logo):</p>
                <canvas id="canvas48" width="48" height="48"></canvas>
                <button onclick="downloadImage('canvas48', 'new_48x48.jpg')">Download 48x48</button>
            </div>
            <div>
                <p>32x32 pixels (Small logo):</p>
                <canvas id="canvas32" width="32" height="32"></canvas>
                <button onclick="downloadImage('canvas32', 'new_32x32.jpg')">Download 32x32</button>
            </div>
            <div>
                <p>24x24 pixels (Very small logo):</p>
                <canvas id="canvas24" width="24" height="24"></canvas>
                <button onclick="downloadImage('canvas24', 'new_24x24.jpg')">Download 24x24</button>
            </div>
        </div>
    </div>

    <script>
        const imageInput = document.getElementById('imageInput');
        const originalCanvas = document.getElementById('originalCanvas');
        const canvas48 = document.getElementById('canvas48');
        const canvas32 = document.getElementById('canvas32');
        const canvas24 = document.getElementById('canvas24');

        imageInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    const img = new Image();
                    img.onload = function() {
                        // Show original
                        originalCanvas.width = img.width;
                        originalCanvas.height = img.height;
                        const originalCtx = originalCanvas.getContext('2d');
                        originalCtx.drawImage(img, 0, 0);

                        // Resize to different sizes
                        resizeImage(img, canvas48, 48, 48);
                        resizeImage(img, canvas32, 32, 32);
                        resizeImage(img, canvas24, 24, 24);
                    };
                    img.src = event.target.result;
                };
                reader.readAsDataURL(file);
            }
        });

        function resizeImage(img, canvas, width, height) {
            const ctx = canvas.getContext('2d');
            ctx.imageSmoothingEnabled = true;
            ctx.imageSmoothingQuality = 'high';
            ctx.drawImage(img, 0, 0, width, height);
        }

        function downloadImage(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/jpeg', 0.9);
            link.click();
        }

        // Auto-load the new.jpg file if it exists
        window.addEventListener('load', function() {
            fetch('new.jpg')
                .then(response => response.blob())
                .then(blob => {
                    const img = new Image();
                    img.onload = function() {
                        // Show original
                        originalCanvas.width = img.width;
                        originalCanvas.height = img.height;
                        const originalCtx = originalCanvas.getContext('2d');
                        originalCtx.drawImage(img, 0, 0);

                        // Resize to different sizes
                        resizeImage(img, canvas48, 48, 48);
                        resizeImage(img, canvas32, 32, 32);
                        resizeImage(img, canvas24, 24, 24);
                    };
                    img.src = URL.createObjectURL(blob);
                })
                .catch(err => console.log('Could not auto-load new.jpg'));
        });
    </script>
</body>
</html>
