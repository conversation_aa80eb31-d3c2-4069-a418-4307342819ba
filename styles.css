/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #ff6b35;
    --primary-dark: #e55a2b;
    --secondary-color: #f59e0b;
    --accent-color: #10b981;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #9ca3af;
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-dark: #111827;
    --border-color: #e5e7eb;
    --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --transition: all 0.3s ease;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 0.5rem;
}

.section-title {
    font-size: 2.5rem;
    text-align: center;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.section-subtitle {
    font-size: 1.125rem;
    text-align: center;
    color: var(--text-secondary);
    margin-bottom: 3rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 12px 24px;
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
    min-width: 120px;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--bg-primary);
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.btn-secondary:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: var(--transition);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.nav-logo i {
    font-size: 1.75rem;
}

.logo-image {
    width: 20px;
    height: 20px;
    object-fit: contain;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition);
}

.nav-link:hover::after {
    width: 100%;
}

/* Reseller Button Styling */
.reseller-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white !important;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    box-shadow: 0 2px 10px rgba(255, 107, 53, 0.3);
    transition: var(--transition);
}

.reseller-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
    color: white !important;
}

.reseller-btn::after {
    display: none;
}

/* Reseller Program Section */
.reseller-program {
    padding: 5rem 0;
    background: linear-gradient(135deg, #f9fafb, #f1f3f4);
    position: relative;
    overflow: hidden;
}

.reseller-program::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e9ecef" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.reseller-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.reseller-text .section-title {
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.reseller-text .section-subtitle {
    color: var(--text-secondary);
    font-size: 1.2rem;
    margin-bottom: 3rem;
}

.reseller-benefits {
    display: grid;
    gap: 2rem;
    margin-bottom: 3rem;
}

.benefit-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    transition: all 0.3s ease;
}

.benefit-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.benefit-item i {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #ff6b35, #e55a2b);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.benefit-item h4 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.benefit-item p {
    color: var(--text-secondary);
    font-size: 0.95rem;
    line-height: 1.5;
}

.reseller-cta {
    text-align: center;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.cta-note {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-style: italic;
}

/* Reseller Image/Preview */
.reseller-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.earnings-preview {
    background: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
    padding: 2rem;
    width: 100%;
    max-width: 350px;
}

.earnings-card {
    text-align: center;
    margin-bottom: 2rem;
    padding: 2rem;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: var(--border-radius);
    color: white;
}

.earnings-card h3 {
    font-size: 1rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.earnings-amount {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.earnings-growth {
    font-size: 0.9rem;
    opacity: 0.8;
}

.stats-mini {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.stat-mini {
    text-align: center;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    transition: var(--transition);
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    overflow: hidden;
    position: relative;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.1;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-tv {
    width: 300px;
    height: 300px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    animation: float 6s ease-in-out infinite;
}

.hero-tv i {
    font-size: 8rem;
    opacity: 0.8;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

/* Free Trial Banner */
.trial-banner {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    padding: 2rem 0;
    position: relative;
    overflow: hidden;
}

.trial-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><defs><pattern id="grain" width="100" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="20" fill="url(%23grain)"/></svg>') repeat;
    opacity: 0.3;
}

.trial-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
    position: relative;
    z-index: 1;
}

.trial-text h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.trial-text p {
    margin: 0;
    opacity: 0.9;
    font-size: 1rem;
}

.trial-action .btn {
    font-size: 1.1rem;
    padding: 0.875rem 2rem;
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.trial-action .btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* Features Section */
.features {
    padding: 80px 0;
    background: var(--bg-secondary);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.feature-card {
    background: var(--bg-primary);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.feature-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Pricing Section */
.pricing {
    padding: 80px 0;
    background: var(--bg-primary);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
}

.pricing-card {
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    position: relative;
    transition: var(--transition);
    box-shadow: var(--shadow-md);
}

.pricing-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.pricing-card.featured {
    border-color: var(--primary-color);
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

.popular-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: white;
    padding: 6px 20px;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
}

.pricing-header h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.price {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: 2rem;
}

.currency {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-secondary);
}

.amount {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin: 0 0.25rem;
}

.period {
    font-size: 1rem;
    color: var(--text-secondary);
}

.pricing-features {
    list-style: none;
    margin-bottom: 2rem;
    text-align: left;
}

.pricing-features li {
    padding: 0.75rem 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    border-bottom: 1px solid var(--border-color);
}

.pricing-features li:last-child {
    border-bottom: none;
}

.pricing-features i {
    color: var(--accent-color);
    font-size: 1rem;
    width: 16px;
}

/* Free Trial Section */
.free-trial {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.free-trial::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.3;
}

.free-trial-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    position: relative;
    z-index: 1;
}

.free-trial-text h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    font-weight: 700;
}

.free-trial-text p {
    font-size: 1.125rem;
    margin-bottom: 2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.trial-features {
    list-style: none;
    margin-bottom: 2rem;
}

.trial-features li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    font-size: 1rem;
}

.trial-features i {
    color: var(--accent-color);
    font-size: 1rem;
}

/* Duplicate removed - already defined in reseller section */

.free-trial-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.trial-badge {
    width: 200px;
    height: 200px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 3px solid rgba(255, 255, 255, 0.2);
    animation: float 6s ease-in-out infinite;
}

.badge-text {
    font-size: 3rem;
    font-weight: 700;
    line-height: 1;
}

.badge-label {
    font-size: 1rem;
    font-weight: 600;
    opacity: 0.9;
}

/* Devices Section */
.devices {
    padding: 80px 0;
    background: var(--bg-primary);
}

.devices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.device-card {
    background: var(--bg-secondary);
    padding: 2rem 1.5rem;
    border-radius: var(--border-radius-lg);
    text-align: center;
    transition: var(--transition);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.device-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.1), transparent);
    transition: var(--transition);
}

.device-card:hover::before {
    left: 100%;
}

.device-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.device-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 1.5rem;
    transition: var(--transition);
}

.device-card:hover .device-icon {
    transform: scale(1.1);
}

.device-card h3 {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.device-card p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background: var(--bg-secondary);
}

.contact-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
}

.contact-card {
    background: var(--bg-primary);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.contact-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.contact-card i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.contact-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.contact-card p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

/* Footer */
.footer {
    background: var(--bg-dark);
    color: white;
    padding: 2rem 0;
    text-align: center;
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

.footer-logo i {
    font-size: 1.75rem;
}

/* Floating Chat Bubble */
.chat-bubble {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    z-index: 1500;
    transition: var(--transition);
    animation: chatBubblePulse 2s infinite;
}

.chat-bubble:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl);
}

.chat-bubble-icon {
    position: relative;
    color: white;
    font-size: 1.5rem;
}

.chat-notification {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--secondary-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
    animation: notificationBounce 1s infinite;
}

.chat-bubble-tooltip {
    position: absolute;
    bottom: 70px;
    right: 0;
    background: var(--bg-dark);
    color: white;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    white-space: nowrap;
    opacity: 0;
    transform: translateY(10px);
    transition: var(--transition);
    pointer-events: none;
}

.chat-bubble:hover .chat-bubble-tooltip {
    opacity: 1;
    transform: translateY(0);
}

@keyframes chatBubblePulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes notificationBounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

/* Enhanced Chat Modal */
.chat-modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.chat-modal-content {
    background-color: var(--bg-primary);
    margin: 2% auto;
    border-radius: var(--border-radius-lg);
    width: 90%;
    max-width: 400px;
    max-height: 600px;
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
    border: 1px solid var(--border-color);
}

@keyframes modalSlideIn {
    from { transform: translateY(-50px) scale(0.9); opacity: 0; }
    to { transform: translateY(0) scale(1); opacity: 1; }
}

.chat-header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.chat-header-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.agent-avatar {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.agent-details h3 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
}

.agent-status {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    opacity: 0.9;
}

.agent-status.online i {
    color: var(--accent-color);
    animation: statusPulse 2s infinite;
}

@keyframes statusPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.chat-actions {
    display: flex;
    gap: 0.5rem;
}

.chat-minimize,
.chat-close {
    background: none;
    border: none;
    color: white;
    font-size: 1rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: var(--transition);
}

.chat-minimize:hover,
.chat-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.chat-body {
    height: 450px;
    display: flex;
    flex-direction: column;
    background: var(--bg-secondary);
}

.chat-messages {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.chat-message {
    display: flex;
    gap: 0.75rem;
    align-items: flex-start;
}

.chat-message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.chat-message.bot .message-avatar {
    background: var(--primary-color);
    color: white;
}

.chat-message.user .message-avatar {
    background: var(--accent-color);
    color: white;
}

.message-content {
    flex: 1;
    max-width: 80%;
}

.message-bubble {
    padding: 0.75rem 1rem;
    border-radius: 18px;
    position: relative;
    word-wrap: break-word;
}

.chat-message.bot .message-bubble {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-bottom-left-radius: 6px;
}

.chat-message.user .message-bubble {
    background: var(--primary-color);
    color: white;
    border-bottom-right-radius: 6px;
}

.message-time {
    font-size: 0.75rem;
    color: var(--text-light);
    margin-top: 0.25rem;
    text-align: left;
}

.chat-message.user .message-time {
    text-align: right;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    padding: 0 1rem 1rem;
}

.typing-dots {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 18px;
    border-bottom-left-radius: 6px;
    padding: 0.75rem 1rem;
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--text-light);
    animation: typingDots 1.4s infinite;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes typingDots {
    0%, 60%, 100% { opacity: 0.3; transform: scale(0.8); }
    30% { opacity: 1; transform: scale(1); }
}

.chat-input-container {
    padding: 1rem;
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
}

.chat-input-wrapper {
    display: flex;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

#chatInput {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 25px;
    font-size: 0.875rem;
    outline: none;
    transition: var(--transition);
}

#chatInput:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

#sendMessage {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.75rem;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#sendMessage:hover {
    background: var(--primary-dark);
    transform: scale(1.05);
}

.chat-quick-replies {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.quick-reply {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    cursor: pointer;
    transition: var(--transition);
}

.quick-reply:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Telegram Button in Chat */
.message-bubble .btn {
    margin-top: 0.75rem;
    width: 100%;
    justify-content: center;
    font-size: 0.875rem;
    padding: 0.75rem 1rem;
}

.message-bubble .btn i {
    margin-right: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: var(--bg-primary);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: var(--transition);
        box-shadow: var(--shadow-lg);
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-tv {
        width: 200px;
        height: 200px;
    }

    .hero-tv i {
        font-size: 5rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .features-grid,
    .pricing-grid {
        grid-template-columns: 1fr;
    }

    .pricing-card.featured {
        transform: none;
    }

    .hero-buttons {
        justify-content: center;
        flex-direction: column;
        align-items: center;
    }

    .trial-content {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .trial-text h3 {
        font-size: 1.25rem;
    }

    .btn {
        min-width: 140px;
    }

    .chat-modal-content {
        margin: 5% auto;
        width: 95%;
        max-height: 90vh;
    }

    .chat-bubble {
        width: 50px;
        height: 50px;
        bottom: 15px;
        right: 15px;
    }

    .chat-bubble-icon {
        font-size: 1.25rem;
    }

    .free-trial-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .free-trial-text h2 {
        font-size: 2rem;
    }

    .trial-badge {
        width: 150px;
        height: 150px;
    }

    .badge-text {
        font-size: 2.5rem;
    }

    .devices-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero {
        padding: 100px 0 60px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .features,
    .pricing,
    .contact {
        padding: 60px 0;
    }

    .section-title {
        font-size: 1.75rem;
    }

    .pricing-card {
        padding: 1.5rem;
    }

    .amount {
        font-size: 2.5rem;
    }

    .devices-grid {
        grid-template-columns: 1fr;
    }

    .device-card {
        padding: 1.5rem 1rem;
    }

    .chat-bubble {
        width: 45px;
        height: 45px;
    }

    .chat-bubble-icon {
        font-size: 1.1rem;
    }

    .chat-notification {
        width: 18px;
        height: 18px;
        font-size: 0.7rem;
    }

    /* Reseller Program Responsive */
    .reseller-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .reseller-benefits {
        gap: 1rem;
    }

    .benefit-item {
        padding: 1rem;
    }

    .earnings-preview {
        max-width: 300px;
        margin: 0 auto;
    }

    .earnings-card {
        padding: 1.5rem;
    }

    .earnings-amount {
        font-size: 2.5rem;
    }

    .stats-mini {
        gap: 0.5rem;
    }

    .stat-mini {
        padding: 0.75rem;
    }

    .stat-number {
        font-size: 1.25rem;
    }
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading animation */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.loading {
    animation: pulse 2s infinite;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    html {
        scroll-behavior: auto;
    }
}

/* Focus styles for accessibility */
.btn:focus,
.nav-link:focus,
#chatInput:focus,
#sendMessage:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-secondary: #000000;
    }
}
