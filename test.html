<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BadBoyz IPTV - Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-passed {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .test-failed {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .test-pending {
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        button {
            background: #6366f1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #4f46e5;
        }
        .checklist {
            list-style-type: none;
            padding: 0;
        }
        .checklist li {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .checklist input[type="checkbox"] {
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <h1>BadBoyz IPTV Website - Testing Suite</h1>
    
    <div class="test-section">
        <h2>Quick Links</h2>
        <button onclick="window.open('index.html', '_blank')">Open Main Website</button>
        <button onclick="window.open('http://localhost:8000', '_blank')">Open Local Server</button>
        <button onclick="testTelegramLink()">Test Telegram Link</button>
    </div>

    <div class="test-section">
        <h2>Automated Tests</h2>
        <button onclick="runAllTests()">Run All Tests</button>
        <div id="test-results"></div>
    </div>

    <div class="test-section">
        <h2>Manual Testing Checklist</h2>
        <ul class="checklist">
            <li>
                <input type="checkbox" id="responsive-desktop">
                <label for="responsive-desktop">Desktop responsiveness (1920x1080, 1366x768)</label>
            </li>
            <li>
                <input type="checkbox" id="responsive-tablet">
                <label for="responsive-tablet">Tablet responsiveness (768x1024, 1024x768)</label>
            </li>
            <li>
                <input type="checkbox" id="responsive-mobile">
                <label for="responsive-mobile">Mobile responsiveness (375x667, 414x896)</label>
            </li>
            <li>
                <input type="checkbox" id="nav-menu">
                <label for="nav-menu">Navigation menu works on all devices</label>
            </li>
            <li>
                <input type="checkbox" id="mobile-menu">
                <label for="mobile-menu">Mobile hamburger menu toggles correctly</label>
            </li>
            <li>
                <input type="checkbox" id="live-chat">
                <label for="live-chat">Live chat modal opens and functions</label>
            </li>
            <li>
                <input type="checkbox" id="chat-responses">
                <label for="chat-responses">Chat bot provides relevant responses</label>
            </li>
            <li>
                <input type="checkbox" id="telegram-redirect">
                <label for="telegram-redirect">Telegram redirect works after timeout</label>
            </li>
            <li>
                <input type="checkbox" id="pricing-plans">
                <label for="pricing-plans">All pricing plan buttons work</label>
            </li>
            <li>
                <input type="checkbox" id="smooth-scroll">
                <label for="smooth-scroll">Smooth scrolling navigation works</label>
            </li>
            <li>
                <input type="checkbox" id="animations">
                <label for="animations">Page animations and transitions work</label>
            </li>
            <li>
                <input type="checkbox" id="browser-chrome">
                <label for="browser-chrome">Works in Chrome</label>
            </li>
            <li>
                <input type="checkbox" id="browser-firefox">
                <label for="browser-firefox">Works in Firefox</label>
            </li>
            <li>
                <input type="checkbox" id="browser-safari">
                <label for="browser-safari">Works in Safari</label>
            </li>
            <li>
                <input type="checkbox" id="browser-edge">
                <label for="browser-edge">Works in Edge</label>
            </li>
            <li>
                <input type="checkbox" id="load-speed">
                <label for="load-speed">Page loads in under 3 seconds</label>
            </li>
            <li>
                <input type="checkbox" id="no-errors">
                <label for="no-errors">No console errors or warnings</label>
            </li>
        </ul>
        <button onclick="generateReport()">Generate Test Report</button>
    </div>

    <div class="test-section">
        <h2>Performance Tests</h2>
        <button onclick="testPageLoad()">Test Page Load Speed</button>
        <button onclick="testResourceLoading()">Test Resource Loading</button>
        <div id="performance-results"></div>
    </div>

    <div class="test-section">
        <h2>Accessibility Tests</h2>
        <button onclick="testKeyboardNavigation()">Test Keyboard Navigation</button>
        <button onclick="testScreenReader()">Test Screen Reader Compatibility</button>
        <div id="accessibility-results"></div>
    </div>

    <script>
        function testTelegramLink() {
            const telegramUrl = 'https://t.me/+sG2pVTojSmg4MGU5';
            window.open(telegramUrl, '_blank');
            alert('Telegram link opened. Please verify it leads to the correct group.');
        }

        function runAllTests() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<h3>Running Tests...</h3>';
            
            const tests = [
                { name: 'HTML Structure', test: testHTMLStructure },
                { name: 'CSS Loading', test: testCSSLoading },
                { name: 'JavaScript Loading', test: testJavaScriptLoading },
                { name: 'External Resources', test: testExternalResources },
                { name: 'Local Storage', test: testLocalStorage },
                { name: 'Responsive Breakpoints', test: testResponsiveBreakpoints }
            ];

            let results = '<h3>Test Results:</h3>';
            
            tests.forEach(test => {
                try {
                    const result = test.test();
                    results += `<div class="test-${result ? 'passed' : 'failed'}">
                        ${test.name}: ${result ? 'PASSED' : 'FAILED'}
                    </div>`;
                } catch (error) {
                    results += `<div class="test-failed">
                        ${test.name}: ERROR - ${error.message}
                    </div>`;
                }
            });

            resultsDiv.innerHTML = results;
        }

        function testHTMLStructure() {
            // Test if main HTML elements exist
            const requiredElements = ['header', 'nav', '.hero', '.features', '.pricing', '.contact', 'footer'];
            return requiredElements.every(selector => {
                const element = parent.document.querySelector(selector);
                return element !== null;
            });
        }

        function testCSSLoading() {
            // Test if CSS is loaded by checking computed styles
            try {
                const testElement = parent.document.querySelector('body');
                const computedStyle = parent.getComputedStyle(testElement);
                return computedStyle.fontFamily.includes('Inter') || computedStyle.fontFamily !== '';
            } catch (error) {
                return false;
            }
        }

        function testJavaScriptLoading() {
            // Test if JavaScript functions are available
            return typeof parent.selectPlan === 'function';
        }

        function testExternalResources() {
            // Test if external resources are accessible
            const fontAwesome = parent.document.querySelector('link[href*="font-awesome"]');
            const googleFonts = parent.document.querySelector('link[href*="fonts.googleapis"]');
            return fontAwesome !== null && googleFonts !== null;
        }

        function testLocalStorage() {
            // Test local storage functionality
            try {
                localStorage.setItem('test', 'value');
                const result = localStorage.getItem('test') === 'value';
                localStorage.removeItem('test');
                return result;
            } catch (error) {
                return false;
            }
        }

        function testResponsiveBreakpoints() {
            // Test CSS media queries
            const mediaQueries = [
                '(max-width: 768px)',
                '(max-width: 480px)',
                '(min-width: 1200px)'
            ];
            
            return mediaQueries.every(query => {
                return window.matchMedia(query) !== null;
            });
        }

        function testPageLoad() {
            const startTime = performance.now();
            const resultsDiv = document.getElementById('performance-results');
            
            // Simulate page load test
            setTimeout(() => {
                const loadTime = performance.now() - startTime;
                resultsDiv.innerHTML = `
                    <h3>Page Load Performance:</h3>
                    <div class="test-${loadTime < 3000 ? 'passed' : 'failed'}">
                        Load Time: ${loadTime.toFixed(2)}ms ${loadTime < 3000 ? '(Good)' : '(Needs Improvement)'}
                    </div>
                `;
            }, 100);
        }

        function testResourceLoading() {
            const resultsDiv = document.getElementById('performance-results');
            const resources = performance.getEntriesByType('resource');
            
            let results = '<h3>Resource Loading:</h3>';
            resources.forEach(resource => {
                const loadTime = resource.responseEnd - resource.startTime;
                results += `<div class="test-${loadTime < 1000 ? 'passed' : 'pending'}">
                    ${resource.name.split('/').pop()}: ${loadTime.toFixed(2)}ms
                </div>`;
            });
            
            resultsDiv.innerHTML = results;
        }

        function testKeyboardNavigation() {
            const resultsDiv = document.getElementById('accessibility-results');
            resultsDiv.innerHTML = `
                <h3>Keyboard Navigation Test:</h3>
                <div class="test-pending">
                    Please test manually:
                    <ul>
                        <li>Tab through all interactive elements</li>
                        <li>Use Enter/Space to activate buttons</li>
                        <li>Use Escape to close modals</li>
                        <li>Ensure focus indicators are visible</li>
                    </ul>
                </div>
            `;
        }

        function testScreenReader() {
            const resultsDiv = document.getElementById('accessibility-results');
            resultsDiv.innerHTML = `
                <h3>Screen Reader Compatibility:</h3>
                <div class="test-pending">
                    Manual testing required with screen reader software:
                    <ul>
                        <li>All images have alt text</li>
                        <li>Headings are properly structured</li>
                        <li>Form elements have labels</li>
                        <li>ARIA attributes are used correctly</li>
                    </ul>
                </div>
            `;
        }

        function generateReport() {
            const checkboxes = document.querySelectorAll('.checklist input[type="checkbox"]');
            const total = checkboxes.length;
            const checked = Array.from(checkboxes).filter(cb => cb.checked).length;
            const percentage = Math.round((checked / total) * 100);
            
            const report = `
                Test Report Generated: ${new Date().toLocaleString()}
                
                Manual Tests Completed: ${checked}/${total} (${percentage}%)
                
                ${Array.from(checkboxes).map(cb => 
                    `${cb.checked ? '✅' : '❌'} ${cb.nextElementSibling.textContent}`
                ).join('\n')}
                
                ${percentage >= 90 ? 'Website is ready for production!' : 
                  percentage >= 70 ? 'Website needs minor fixes before production.' : 
                  'Website requires significant testing and fixes.'}
            `;
            
            alert(report);
            console.log(report);
        }

        // Auto-save checklist state
        document.addEventListener('change', function(e) {
            if (e.target.type === 'checkbox') {
                localStorage.setItem('test-' + e.target.id, e.target.checked);
            }
        });

        // Load saved checklist state
        window.addEventListener('load', function() {
            document.querySelectorAll('.checklist input[type="checkbox"]').forEach(cb => {
                const saved = localStorage.getItem('test-' + cb.id);
                if (saved === 'true') {
                    cb.checked = true;
                }
            });
        });
    </script>
</body>
</html>
