-- BadBoyz IPTV Database Setup - Fixed Version
-- Import this file in phpMyAdmin

-- 1. Admin/Reseller Users Table
CREATE TABLE `reseller_admins` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL,
    `password_hash` varchar(255) NOT NULL,
    `email` varchar(100) NOT NULL,
    `full_name` varchar(100) NOT NULL,
    `role` enum('admin','reseller') DEFAULT 'reseller',
    `status` enum('active','inactive','suspended') DEFAULT 'active',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `last_login` timestamp NULL DEFAULT NULL,
    `login_attempts` int(3) DEFAULT 0,
    `locked_until` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `username` (`username`),
    KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. Customer/Users Table
CREATE TABLE `customers` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL,
    `email` varchar(100) NOT NULL,
    `password_hash` varchar(255) NOT NULL,
    `full_name` varchar(100) NOT NULL,
    `phone` varchar(20) DEFAULT NULL,
    `status` enum('active','expired','suspended','pending') DEFAULT 'pending',
    `max_devices` int(2) DEFAULT 2,
    `current_devices` int(2) DEFAULT 0,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `created_by` int(11) DEFAULT NULL,
    `last_login` timestamp NULL DEFAULT NULL,
    `notes` text DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `username` (`username`),
    UNIQUE KEY `email` (`email`),
    KEY `status` (`status`),
    KEY `created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. Subscription Plans Table
CREATE TABLE `subscription_plans` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL,
    `price` decimal(10,2) NOT NULL,
    `duration_months` int(3) NOT NULL,
    `max_devices` int(2) DEFAULT 2,
    `features` json DEFAULT NULL,
    `status` enum('active','inactive') DEFAULT 'active',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `name` (`name`),
    KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. Customer Subscriptions Table
CREATE TABLE `customer_subscriptions` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `customer_id` int(11) NOT NULL,
    `plan_id` int(11) NOT NULL,
    `start_date` date NOT NULL,
    `end_date` date NOT NULL,
    `status` enum('active','expired','suspended','cancelled') DEFAULT 'active',
    `auto_renew` boolean DEFAULT FALSE,
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    `created_by` int(11) DEFAULT NULL,
    `notes` text DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `customer_id` (`customer_id`),
    KEY `plan_id` (`plan_id`),
    KEY `end_date` (`end_date`),
    KEY `status` (`status`),
    KEY `created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. Payments Table
CREATE TABLE `payments` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `customer_id` int(11) NOT NULL,
    `subscription_id` int(11) DEFAULT NULL,
    `amount` decimal(10,2) NOT NULL,
    `currency` varchar(3) DEFAULT 'USD',
    `payment_method` varchar(50) DEFAULT NULL,
    `transaction_id` varchar(100) DEFAULT NULL,
    `status` enum('pending','completed','failed','refunded') DEFAULT 'pending',
    `payment_date` timestamp DEFAULT CURRENT_TIMESTAMP,
    `processed_by` int(11) DEFAULT NULL,
    `notes` text DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `customer_id` (`customer_id`),
    KEY `subscription_id` (`subscription_id`),
    KEY `status` (`status`),
    KEY `payment_date` (`payment_date`),
    KEY `processed_by` (`processed_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 6. Device Sessions Table
CREATE TABLE `device_sessions` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `customer_id` int(11) NOT NULL,
    `device_id` varchar(100) NOT NULL,
    `device_name` varchar(100) DEFAULT NULL,
    `ip_address` varchar(45) DEFAULT NULL,
    `user_agent` text DEFAULT NULL,
    `last_activity` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `status` enum('active','inactive') DEFAULT 'active',
    `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `customer_id` (`customer_id`),
    KEY `device_id` (`device_id`),
    KEY `status` (`status`),
    KEY `last_activity` (`last_activity`),
    UNIQUE KEY `customer_device` (`customer_id`, `device_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add Foreign Key Constraints (after all tables are created)
ALTER TABLE `customers` ADD CONSTRAINT `fk_customers_created_by` FOREIGN KEY (`created_by`) REFERENCES `reseller_admins`(`id`) ON DELETE SET NULL;

ALTER TABLE `customer_subscriptions` ADD CONSTRAINT `fk_subscriptions_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE CASCADE;
ALTER TABLE `customer_subscriptions` ADD CONSTRAINT `fk_subscriptions_plan` FOREIGN KEY (`plan_id`) REFERENCES `subscription_plans`(`id`) ON DELETE RESTRICT;
ALTER TABLE `customer_subscriptions` ADD CONSTRAINT `fk_subscriptions_created_by` FOREIGN KEY (`created_by`) REFERENCES `reseller_admins`(`id`) ON DELETE SET NULL;

ALTER TABLE `payments` ADD CONSTRAINT `fk_payments_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE CASCADE;
ALTER TABLE `payments` ADD CONSTRAINT `fk_payments_subscription` FOREIGN KEY (`subscription_id`) REFERENCES `customer_subscriptions`(`id`) ON DELETE SET NULL;
ALTER TABLE `payments` ADD CONSTRAINT `fk_payments_processed_by` FOREIGN KEY (`processed_by`) REFERENCES `reseller_admins`(`id`) ON DELETE SET NULL;

ALTER TABLE `device_sessions` ADD CONSTRAINT `fk_devices_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers`(`id`) ON DELETE CASCADE;

-- Insert default admin user first (password will be set via create_admin.php)
INSERT INTO `reseller_admins` (`username`, `password_hash`, `email`, `full_name`, `role`, `status`) VALUES
('Badboyztv', 'TEMP_HASH_TO_BE_UPDATED', '<EMAIL>', 'BadBoyz Admin', 'admin', 'active');

-- Insert default subscription plans
INSERT INTO `subscription_plans` (`name`, `price`, `duration_months`, `max_devices`) VALUES
('Basic', 25.00, 1, 2),
('Standard', 30.00, 1, 3),
('Premium', 35.00, 1, 4);

-- Insert sample customers for testing (passwords are hashed for 'password123')
INSERT INTO `customers` (`username`, `email`, `password_hash`, `full_name`, `phone`, `status`, `max_devices`, `created_by`) VALUES
('johnsmith123', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'John Smith', '******-0101', 'active', 3, 1),
('sarahj456', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Sarah Johnson', '******-0102', 'active', 2, 1),
('mikew789', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Mike Wilson', '******-0103', 'expired', 1, 1);

-- Insert sample subscriptions
INSERT INTO `customer_subscriptions` (`customer_id`, `plan_id`, `start_date`, `end_date`, `status`, `created_by`) VALUES 
(1, 2, '2024-12-15', '2025-02-15', 'active', 1),
(2, 1, '2024-12-20', '2025-02-20', 'active', 1),
(3, 1, '2024-11-10', '2025-01-10', 'expired', 1);

-- Insert sample payments
INSERT INTO `payments` (`customer_id`, `subscription_id`, `amount`, `status`, `processed_by`) VALUES 
(1, 1, 30.00, 'completed', 1),
(2, 2, 25.00, 'completed', 1),
(3, 3, 25.00, 'completed', 1);
