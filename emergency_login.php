<?php
/**
 * Emergency Login - No .htaccess dependencies
 * Visit: https://badboyzmedia.org/emergency_login.php
 */

// Start session
session_start();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Emergency Login - BadBoyz IPTV</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 600px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .success { background: #d4edda; padding: 15px; border-radius: 4px; margin: 10px 0; color: #155724; }
        .error { background: #f8d7da; padding: 15px; border-radius: 4px; margin: 10px 0; color: #721c24; }
        .info { background: #d1ecf1; padding: 15px; border-radius: 4px; margin: 10px 0; color: #0c5460; }
        form { background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 10px 0; }
        input, button { padding: 10px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; border: none; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 Emergency Login</h1>
        <p>Direct login without API dependencies</p>

<?php
// Handle login
if (isset($_POST['login'])) {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if ($username === 'Badboyztv' && $password === 'Skyblue14!') {
        try {
            require_once 'config/database.php';
            $db = getDB();
            
            // Get admin from database
            $stmt = $db->prepare("SELECT * FROM reseller_admins WHERE username = ? AND status = 'active'");
            $stmt->execute([$username]);
            $admin = $stmt->fetch();
            
            if ($admin && password_verify($password, $admin['password'])) {
                // Generate token
                $token = bin2hex(random_bytes(32));
                
                // Store in session
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['username'] = $admin['username'];
                $_SESSION['role'] = $admin['role'];
                $_SESSION['token'] = $token;
                $_SESSION['login_time'] = time();
                
                // Store in database
                $stmt = $db->prepare("
                    UPDATE reseller_admins 
                    SET last_login = NOW(), auth_token = ?, token_expires = DATE_ADD(NOW(), INTERVAL 24 HOUR)
                    WHERE id = ?
                ");
                $stmt->execute([$token, $admin['id']]);
                
                echo "<div class='success'>";
                echo "<h3>✅ Login Successful!</h3>";
                echo "<p><strong>Token:</strong> " . substr($token, 0, 30) . "...</p>";
                echo "<p><strong>User:</strong> " . htmlspecialchars($admin['full_name']) . "</p>";
                echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
                echo "</div>";
                
                // JavaScript to store token and redirect
                echo "<script>";
                echo "localStorage.setItem('authToken', '$token');";
                echo "console.log('Token stored:', '$token');";
                echo "setTimeout(function() { window.location.href = 'reseller-panel.html'; }, 2000);";
                echo "</script>";
                
                echo "<div class='info'>";
                echo "<p>🔄 Redirecting to reseller panel in 2 seconds...</p>";
                echo "<p>Or click: <a href='reseller-panel.html'>Open Reseller Panel</a></p>";
                echo "</div>";
                
            } else {
                echo "<div class='error'>❌ Invalid password or user not found</div>";
            }
        } catch (Exception $e) {
            echo "<div class='error'>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</div>";
        }
    } else {
        echo "<div class='error'>❌ Invalid credentials</div>";
    }
}

// Show current session
if (!empty($_SESSION)) {
    echo "<h3>Current Session:</h3>";
    echo "<div class='info'>";
    echo "<pre>" . json_encode($_SESSION, JSON_PRETTY_PRINT) . "</pre>";
    echo "</div>";
}

// Login form
if (!isset($_SESSION['admin_id'])) {
    echo "<h3>Login:</h3>";
    echo "<form method='post'>";
    echo "<div>";
    echo "<label>Username:</label><br>";
    echo "<input type='text' name='username' value='Badboyztv' required>";
    echo "</div>";
    echo "<div>";
    echo "<label>Password:</label><br>";
    echo "<input type='password' name='password' value='Skyblue14!' required>";
    echo "</div>";
    echo "<div>";
    echo "<button type='submit' name='login'>🔑 Emergency Login</button>";
    echo "</div>";
    echo "</form>";
} else {
    echo "<h3>Quick Actions:</h3>";
    echo "<p><a href='reseller-panel.html' style='background:#28a745;color:white;padding:10px 20px;text-decoration:none;border-radius:4px;'>📊 Open Reseller Panel</a></p>";
    echo "<p><a href='?logout=1' style='background:#dc3545;color:white;padding:10px 20px;text-decoration:none;border-radius:4px;'>🚪 Logout</a></p>";
}

// Logout
if (isset($_GET['logout'])) {
    session_destroy();
    echo "<script>localStorage.removeItem('authToken'); window.location.href = '?';</script>";
}
?>

        <hr>
        <h3>📋 Instructions:</h3>
        <ol>
            <li>This bypasses all .htaccess issues</li>
            <li>Click "Emergency Login" to authenticate</li>
            <li>Token will be stored automatically</li>
            <li>You'll be redirected to the reseller panel</li>
            <li>All functions should work normally</li>
        </ol>
        
        <p><strong>🔒 DELETE this file after the system is working!</strong></p>
    </div>
</body>
</html>
