<?php
/**
 * Complete Authentication Test
 * This will test the entire authentication flow
 */

require_once 'config/database.php';

// Start session
session_start();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Complete Auth Test - BadBoyz IPTV</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 900px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .success { background: #d4edda; padding: 15px; border-radius: 4px; margin: 10px 0; color: #155724; }
        .error { background: #f8d7da; padding: 15px; border-radius: 4px; margin: 10px 0; color: #721c24; }
        .info { background: #d1ecf1; padding: 15px; border-radius: 4px; margin: 10px 0; color: #0c5460; }
        .warning { background: #fff3cd; padding: 15px; border-radius: 4px; margin: 10px 0; color: #856404; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        #results { margin-top: 20px; }
        .step { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Complete Authentication Test</h1>
        
        <div class="info">
            <h3>Test Steps:</h3>
            <ol>
                <li>Check database connection</li>
                <li>Verify admin user exists</li>
                <li>Test password verification</li>
                <li>Test login API</li>
                <li>Test auth verification API</li>
                <li>Test dashboard API</li>
            </ol>
        </div>

        <?php
        // Step 1: Database Connection Test
        echo "<div class='step'>";
        echo "<h3>Step 1: Database Connection</h3>";
        try {
            $db = getDB();
            echo "<div class='success'>✅ Database connection successful</div>";
        } catch (Exception $e) {
            echo "<div class='error'>❌ Database connection failed: " . $e->getMessage() . "</div>";
            exit;
        }
        echo "</div>";

        // Step 2: Check Admin User
        echo "<div class='step'>";
        echo "<h3>Step 2: Admin User Check</h3>";
        try {
            $stmt = $db->prepare("SELECT id, username, password_hash, email, full_name, role, status FROM reseller_admins WHERE username = ?");
            $stmt->execute(['Badboyztv']);
            $admin = $stmt->fetch();
            
            if ($admin) {
                echo "<div class='success'>✅ Admin user found</div>";
                echo "<pre>" . json_encode($admin, JSON_PRETTY_PRINT) . "</pre>";
            } else {
                echo "<div class='error'>❌ Admin user not found</div>";
                exit;
            }
        } catch (Exception $e) {
            echo "<div class='error'>❌ Error checking admin user: " . $e->getMessage() . "</div>";
            exit;
        }
        echo "</div>";

        // Step 3: Password Verification Test
        echo "<div class='step'>";
        echo "<h3>Step 3: Password Verification</h3>";
        if (verifyPassword('Skyblue14!', $admin['password_hash'])) {
            echo "<div class='success'>✅ Password verification successful</div>";
        } else {
            echo "<div class='error'>❌ Password verification failed</div>";
            echo "<p>Attempting to fix password...</p>";
            
            // Fix password
            $newHash = hashPassword('Skyblue14!');
            $stmt = $db->prepare("UPDATE reseller_admins SET password_hash = ? WHERE username = ?");
            if ($stmt->execute([$newHash, 'Badboyztv'])) {
                echo "<div class='success'>✅ Password updated successfully</div>";
                $admin['password_hash'] = $newHash;
            } else {
                echo "<div class='error'>❌ Failed to update password</div>";
                exit;
            }
        }
        echo "</div>";
        ?>

        <div class="step">
            <h3>Step 4: API Tests</h3>
            <button class="btn-primary" onclick="testLogin()">🔑 Test Login API</button>
            <button class="btn-success" onclick="testAuthVerify()">🔐 Test Auth Verify</button>
            <button class="btn-success" onclick="testDashboard()">📊 Test Dashboard</button>
            <button class="btn-danger" onclick="clearResults()">🗑️ Clear Results</button>
        </div>

        <div id="results"></div>

        <div class="info">
            <h3>Quick Links:</h3>
            <p><a href="emergency_login.php" target="_blank">🚨 Emergency Login</a></p>
            <p><a href="debug_auth.php" target="_blank">🔍 Debug Auth</a></p>
            <p><a href="reseller-panel.html" target="_blank">📊 Reseller Panel</a></p>
        </div>
    </div>

    <script>
        let authToken = null;

        function addResult(title, content, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `<h4>${title}</h4><pre>${JSON.stringify(content, null, 2)}</pre>`;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testLogin() {
            console.log('=== Login API Test ===');
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        username: 'Badboyztv',
                        password: 'Skyblue14!'
                    })
                });
                
                const data = await response.json();
                console.log('Login response:', data);
                
                if (response.ok && data.success && data.data.token) {
                    authToken = data.data.token;
                    localStorage.setItem('authToken', authToken);
                    addResult('🔑 Login API Test', {
                        status: response.status,
                        success: true,
                        token: authToken.substring(0, 20) + '...',
                        user: data.data.user
                    }, 'success');
                } else {
                    addResult('🔑 Login API Test', {
                        status: response.status,
                        statusText: response.statusText,
                        data: data
                    }, 'error');
                }
            } catch (error) {
                console.error('Login error:', error);
                addResult('🔑 Login API Test', {error: error.message}, 'error');
            }
        }

        async function testAuthVerify() {
            console.log('=== Auth Verify Test ===');
            const token = authToken || localStorage.getItem('authToken');
            
            if (!token) {
                addResult('🔐 Auth Verify Test', {error: 'No token available. Please login first.'}, 'error');
                return;
            }

            try {
                const response = await fetch('/api/auth/verify', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                console.log('Auth verify response:', data);
                
                addResult('🔐 Auth Verify Test', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, response.ok ? 'success' : 'error');
            } catch (error) {
                console.error('Auth verify error:', error);
                addResult('🔐 Auth Verify Test', {error: error.message}, 'error');
            }
        }

        async function testDashboard() {
            console.log('=== Dashboard Test ===');
            const token = authToken || localStorage.getItem('authToken');
            
            if (!token) {
                addResult('📊 Dashboard Test', {error: 'No token available. Please login first.'}, 'error');
                return;
            }

            try {
                const response = await fetch('/api/dashboard', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                console.log('Dashboard response:', data);
                
                addResult('📊 Dashboard Test', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, response.ok ? 'success' : 'error');
            } catch (error) {
                console.error('Dashboard error:', error);
                addResult('📊 Dashboard Test', {error: error.message}, 'error');
            }
        }

        // Auto-load token from localStorage
        window.addEventListener('load', function() {
            const storedToken = localStorage.getItem('authToken');
            if (storedToken) {
                authToken = storedToken;
                console.log('Loaded token from localStorage:', storedToken.substring(0, 20) + '...');
            }
        });
    </script>
</body>
</html>
