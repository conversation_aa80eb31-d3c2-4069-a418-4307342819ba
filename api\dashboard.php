<?php
/**
 * Dashboard API Endpoints
 */

require_once 'auth.php';

function handleDashboardRequest($method, $pathParts, $input) {
    switch ($method) {
        case 'GET':
            if (isset($pathParts[1])) {
                switch ($pathParts[1]) {
                    case 'stats':
                        getDashboardStats();
                        break;
                    case 'recent':
                        getRecentActivity();
                        break;
                    case 'revenue':
                        getRevenueStats();
                        break;
                    default:
                        errorResponse('Invalid dashboard endpoint', 404);
                }
            } else {
                getDashboardOverview();
            }
            break;
            
        default:
            errorResponse('Method not allowed', 405);
    }
}

function getDashboardOverview() {
    $adminId = requireAuth();
    
    $db = getDB();
    
    // Get customer statistics
    $stmt = $db->query("
        SELECT 
            COUNT(*) as total_customers,
            SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_customers,
            SUM(CASE WHEN status = 'expired' THEN 1 ELSE 0 END) as expired_customers,
            SUM(CASE WHEN status = 'suspended' THEN 1 ELSE 0 END) as suspended_customers
        FROM customers
    ");
    $customerStats = $stmt->fetch();
    
    // Get customers due soon (within 7 days)
    $stmt = $db->query("
        SELECT COUNT(*) as due_soon
        FROM customers c
        JOIN customer_subscriptions cs ON c.id = cs.customer_id
        WHERE cs.status = 'active' 
        AND cs.end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
    ");
    $dueSoon = $stmt->fetch()['due_soon'];
    
    // Get revenue statistics
    $stmt = $db->query("
        SELECT 
            SUM(CASE WHEN MONTH(payment_date) = MONTH(CURDATE()) AND YEAR(payment_date) = YEAR(CURDATE()) THEN amount ELSE 0 END) as monthly_revenue,
            SUM(amount) as total_revenue,
            COUNT(CASE WHEN MONTH(payment_date) = MONTH(CURDATE()) AND YEAR(payment_date) = YEAR(CURDATE()) THEN 1 END) as monthly_payments
        FROM payments 
        WHERE status = 'completed'
    ");
    $revenueStats = $stmt->fetch();
    
    // Get subscription statistics
    $stmt = $db->query("
        SELECT 
            sp.name,
            COUNT(*) as count,
            SUM(sp.price) as revenue
        FROM customer_subscriptions cs
        JOIN subscription_plans sp ON cs.plan_id = sp.id
        WHERE cs.status = 'active'
        GROUP BY sp.id, sp.name
        ORDER BY count DESC
    ");
    $planStats = $stmt->fetchAll();
    
    successResponse([
        'customer_stats' => [
            'total' => (int)$customerStats['total_customers'],
            'active' => (int)$customerStats['active_customers'],
            'expired' => (int)$customerStats['expired_customers'],
            'suspended' => (int)$customerStats['suspended_customers'],
            'due_soon' => (int)$dueSoon
        ],
        'revenue_stats' => [
            'monthly_revenue' => (float)($revenueStats['monthly_revenue'] ?? 0),
            'total_revenue' => (float)($revenueStats['total_revenue'] ?? 0),
            'monthly_payments' => (int)($revenueStats['monthly_payments'] ?? 0)
        ],
        'plan_stats' => $planStats
    ]);
}

function getDashboardStats() {
    $adminId = requireAuth();
    
    $db = getDB();
    
    // Get detailed statistics with trends
    $stmt = $db->query("
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as new_customers
        FROM customers 
        WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        GROUP BY DATE(created_at)
        ORDER BY date DESC
    ");
    $customerTrend = $stmt->fetchAll();
    
    $stmt = $db->query("
        SELECT 
            DATE(payment_date) as date,
            SUM(amount) as revenue,
            COUNT(*) as payments
        FROM payments 
        WHERE payment_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
        AND status = 'completed'
        GROUP BY DATE(payment_date)
        ORDER BY date DESC
    ");
    $revenueTrend = $stmt->fetchAll();
    
    // Get expiring subscriptions
    $stmt = $db->query("
        SELECT 
            c.username,
            c.full_name,
            c.email,
            cs.end_date,
            sp.name as plan_name,
            DATEDIFF(cs.end_date, CURDATE()) as days_remaining
        FROM customers c
        JOIN customer_subscriptions cs ON c.id = cs.customer_id
        JOIN subscription_plans sp ON cs.plan_id = sp.id
        WHERE cs.status = 'active' 
        AND cs.end_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 30 DAY)
        ORDER BY cs.end_date ASC
        LIMIT 10
    ");
    $expiringSubscriptions = $stmt->fetchAll();
    
    successResponse([
        'customer_trend' => $customerTrend,
        'revenue_trend' => $revenueTrend,
        'expiring_subscriptions' => $expiringSubscriptions
    ]);
}

function getRecentActivity() {
    $adminId = requireAuth();
    
    $db = getDB();
    
    // Get recent customers
    $stmt = $db->query("
        SELECT 
            c.username,
            c.full_name,
            c.email,
            c.created_at,
            ra.username as created_by
        FROM customers c
        LEFT JOIN reseller_admins ra ON c.created_by = ra.id
        ORDER BY c.created_at DESC
        LIMIT 10
    ");
    $recentCustomers = $stmt->fetchAll();
    
    // Get recent payments
    $stmt = $db->query("
        SELECT 
            p.amount,
            p.payment_date,
            p.status,
            c.username,
            c.full_name,
            ra.username as processed_by
        FROM payments p
        JOIN customers c ON p.customer_id = c.id
        LEFT JOIN reseller_admins ra ON p.processed_by = ra.id
        ORDER BY p.payment_date DESC
        LIMIT 10
    ");
    $recentPayments = $stmt->fetchAll();
    
    // Get recent subscriptions
    $stmt = $db->query("
        SELECT 
            cs.start_date,
            cs.end_date,
            cs.status,
            c.username,
            c.full_name,
            sp.name as plan_name,
            sp.price
        FROM customer_subscriptions cs
        JOIN customers c ON cs.customer_id = c.id
        JOIN subscription_plans sp ON cs.plan_id = sp.id
        ORDER BY cs.created_at DESC
        LIMIT 10
    ");
    $recentSubscriptions = $stmt->fetchAll();
    
    successResponse([
        'recent_customers' => $recentCustomers,
        'recent_payments' => $recentPayments,
        'recent_subscriptions' => $recentSubscriptions
    ]);
}

function getRevenueStats() {
    $adminId = requireAuth();
    
    $db = getDB();
    
    // Get monthly revenue for the last 12 months
    $stmt = $db->query("
        SELECT 
            YEAR(payment_date) as year,
            MONTH(payment_date) as month,
            SUM(amount) as revenue,
            COUNT(*) as payments
        FROM payments 
        WHERE payment_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
        AND status = 'completed'
        GROUP BY YEAR(payment_date), MONTH(payment_date)
        ORDER BY year DESC, month DESC
    ");
    $monthlyRevenue = $stmt->fetchAll();
    
    // Get revenue by plan
    $stmt = $db->query("
        SELECT 
            sp.name,
            sp.price,
            COUNT(p.id) as payment_count,
            SUM(p.amount) as total_revenue
        FROM subscription_plans sp
        LEFT JOIN customer_subscriptions cs ON sp.id = cs.plan_id
        LEFT JOIN payments p ON cs.customer_id = p.customer_id AND p.status = 'completed'
        GROUP BY sp.id, sp.name, sp.price
        ORDER BY total_revenue DESC
    ");
    $revenueByPlan = $stmt->fetchAll();
    
    // Get top customers by revenue
    $stmt = $db->query("
        SELECT 
            c.username,
            c.full_name,
            c.email,
            SUM(p.amount) as total_paid,
            COUNT(p.id) as payment_count
        FROM customers c
        JOIN payments p ON c.id = p.customer_id
        WHERE p.status = 'completed'
        GROUP BY c.id
        ORDER BY total_paid DESC
        LIMIT 10
    ");
    $topCustomers = $stmt->fetchAll();
    
    successResponse([
        'monthly_revenue' => $monthlyRevenue,
        'revenue_by_plan' => $revenueByPlan,
        'top_customers' => $topCustomers
    ]);
}
?>
