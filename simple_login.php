<?php
/**
 * Simple Login Test - Bypass ModSecurity
 * Visit: https://badboyzmedia.org/simple_login.php
 * DELETE this file after testing!
 */

// Configure session settings
ini_set('session.cookie_httponly', 1);
ini_set('session.use_strict_mode', 1);
ini_set('session.cookie_samesite', 'Lax');
session_start();

echo "<h2>🔧 Simple Login Test</h2>";

// <PERSON>le login
if (isset($_POST['login'])) {
    require_once 'config/database.php';
    
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if ($username === 'Badboyztv' && $password === 'Skyblue14!') {
        try {
            $db = getDB();
            
            // Get admin from database
            $stmt = $db->prepare("SELECT * FROM reseller_admins WHERE username = ? AND status = 'active'");
            $stmt->execute([$username]);
            $admin = $stmt->fetch();
            
            if ($admin && password_verify($password, $admin['password'])) {
                // Generate token
                $token = bin2hex(random_bytes(32));
                
                // Store in session
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['username'] = $admin['username'];
                $_SESSION['role'] = $admin['role'];
                $_SESSION['token'] = $token;
                $_SESSION['login_time'] = time();
                
                // Store in database
                $stmt = $db->prepare("
                    UPDATE reseller_admins 
                    SET last_login = NOW(), auth_token = ?, token_expires = DATE_ADD(NOW(), INTERVAL 24 HOUR)
                    WHERE id = ?
                ");
                $stmt->execute([$token, $admin['id']]);
                
                echo "<div style='background:#d4edda;padding:15px;border-radius:4px;margin:10px 0;'>";
                echo "<h3>✅ Login Successful!</h3>";
                echo "<p>Token: " . substr($token, 0, 20) . "...</p>";
                echo "<p>Session ID: " . session_id() . "</p>";
                echo "<p>Admin ID: " . $admin['id'] . "</p>";
                echo "</div>";
                
                // Store token in localStorage via JavaScript
                echo "<script>";
                echo "localStorage.setItem('authToken', '$token');";
                echo "console.log('Token stored in localStorage:', '$token');";
                echo "</script>";
                
            } else {
                echo "<div style='background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;'>";
                echo "❌ Invalid password or user not found<br>";
                echo "</div>";
            }
        } catch (Exception $e) {
            echo "<div style='background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;'>";
            echo "❌ Database error: " . htmlspecialchars($e->getMessage()) . "<br>";
            echo "</div>";
        }
    } else {
        echo "<div style='background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;'>";
        echo "❌ Invalid credentials<br>";
        echo "</div>";
    }
}

// Show current session
echo "<h3>Current Session:</h3>";
if (empty($_SESSION)) {
    echo "<p>❌ No session data</p>";
} else {
    echo "<div style='background:#d1ecf1;padding:10px;border-radius:4px;margin:10px 0;'>";
    echo "<pre>" . json_encode($_SESSION, JSON_PRETTY_PRINT) . "</pre>";
    echo "</div>";
}

// Login form
if (!isset($_SESSION['admin_id'])) {
    echo "<h3>Login:</h3>";
    echo "<form method='post'>";
    echo "<p>Username: <input type='text' name='username' value='Badboyztv' required></p>";
    echo "<p>Password: <input type='password' name='password' value='Skyblue14!' required></p>";
    echo "<p><button type='submit' name='login'>Login</button></p>";
    echo "</form>";
} else {
    echo "<h3>Actions:</h3>";
    echo "<p><a href='reseller-panel.html' target='_blank'>Open Reseller Panel</a></p>";
    echo "<p><a href='?logout=1'>Logout</a> | <a href='?'>Refresh</a></p>";
    
    echo "<h3>Test Reseller Panel:</h3>";
    echo "<p>1. Click 'Open Reseller Panel' above</p>";
    echo "<p>2. The token has been stored in localStorage automatically</p>";
    echo "<p>3. The reseller panel should work without needing to login again</p>";
    echo "<p>4. All navigation buttons should work</p>";
}

// Logout
if (isset($_GET['logout'])) {
    session_destroy();
    echo "<script>localStorage.removeItem('authToken'); window.location.href = '?';</script>";
}

echo "<hr>";
echo "<h3>📋 Instructions:</h3>";
echo "<ol>";
echo "<li>Click 'Login' to authenticate</li>";
echo "<li>Token will be stored in both session and localStorage</li>";
echo "<li>Open reseller panel - it should work without login</li>";
echo "<li>Test all navigation functions</li>";
echo "<li>This bypasses the ModSecurity 406 error</li>";
echo "</ol>";

echo "<p>🔒 <strong>DELETE this file after testing!</strong></p>";

echo "<style>body{font-family:Arial,sans-serif;margin:20px;} h2{color:#333;} h3{color:#666;border-bottom:1px solid #eee;} pre{background:#f5f5f5;padding:10px;border-radius:4px;overflow-x:auto;} form{background:#f8f9fa;padding:15px;border-radius:4px;} input,button{padding:8px;margin:5px;} a{color:#007bff;text-decoration:none;} a:hover{text-decoration:underline;}</style>";
?>
