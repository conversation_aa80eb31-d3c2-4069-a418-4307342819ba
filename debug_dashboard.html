<!DOCTYPE html>
<html>
<head>
    <title>Dashboard Debug - BadBoyz IPTV</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 900px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .success { background: #d4edda; padding: 15px; border-radius: 4px; margin: 10px 0; color: #155724; }
        .error { background: #f8d7da; padding: 15px; border-radius: 4px; margin: 10px 0; color: #721c24; }
        .info { background: #d1ecf1; padding: 15px; border-radius: 4px; margin: 10px 0; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; background: #007bff; color: white; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        .debug-section { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
        .hidden { display: none !important; }
        
        /* Simulate the actual dashboard styles */
        .login-screen { 
            display: flex; 
            justify-content: center; 
            align-items: center; 
            min-height: 100vh; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .dashboard { 
            display: flex; 
            min-height: 100vh; 
            background: #f8f9fa;
        }
        .dashboard.hidden { display: none; }
        .login-screen.hidden { display: none; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Dashboard Debug Tool</h1>
        <p>This tool helps debug the dashboard redirection issue</p>
        
        <div class="debug-section">
            <h3>Current Status</h3>
            <div id="statusInfo"></div>
        </div>

        <div class="debug-section">
            <h3>DOM Elements Check</h3>
            <button onclick="checkElements()">🔍 Check DOM Elements</button>
            <div id="elementsInfo"></div>
        </div>

        <div class="debug-section">
            <h3>Authentication Test</h3>
            <button onclick="testLogin()">🔑 Test Login</button>
            <button onclick="testDashboardShow()">📊 Force Show Dashboard</button>
            <button onclick="testLoginShow()">🔓 Force Show Login</button>
            <div id="authInfo"></div>
        </div>

        <div class="debug-section">
            <h3>Console Logs</h3>
            <div id="consoleLogs" style="max-height: 300px; overflow-y: auto; background: #000; color: #0f0; padding: 10px; font-family: monospace;"></div>
        </div>
    </div>

    <!-- Simulate the actual login/dashboard structure -->
    <div id="loginScreen" class="login-screen">
        <div style="background: white; padding: 30px; border-radius: 8px; text-align: center;">
            <h2>Login Screen</h2>
            <p>This simulates the actual login screen</p>
            <input type="text" id="username" placeholder="Username" value="Badboyztv" style="display: block; margin: 10px auto; padding: 10px; width: 200px;">
            <input type="password" id="password" placeholder="Password" value="Skyblue14!" style="display: block; margin: 10px auto; padding: 10px; width: 200px;">
            <button onclick="simulateLogin()" style="margin-top: 10px;">Login</button>
        </div>
    </div>

    <div id="dashboard" class="dashboard hidden">
        <div style="padding: 30px; text-align: center; width: 100%;">
            <h2>Dashboard</h2>
            <p>This simulates the actual dashboard</p>
            <p>✅ Dashboard is now visible!</p>
            <button onclick="showLoginForm()">Back to Login</button>
        </div>
    </div>

    <script>
        let authToken = localStorage.getItem('authToken');
        let logs = [];

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${message}`;
            logs.push(logMessage);
            console.log(logMessage);
            updateConsoleLogs();
        }

        function updateConsoleLogs() {
            const consoleDiv = document.getElementById('consoleLogs');
            consoleDiv.innerHTML = logs.slice(-20).join('<br>');
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }

        function updateStatus() {
            const statusDiv = document.getElementById('statusInfo');
            const hasToken = !!authToken;
            const loginVisible = !document.getElementById('loginScreen').classList.contains('hidden');
            const dashboardVisible = !document.getElementById('dashboard').classList.contains('hidden');
            
            statusDiv.innerHTML = `
                <div class="${hasToken ? 'success' : 'error'}">
                    Token: ${hasToken ? '✅ Present (' + authToken.substring(0, 20) + '...)' : '❌ Missing'}
                </div>
                <div class="${loginVisible ? 'info' : 'success'}">
                    Login Screen: ${loginVisible ? '👁️ Visible' : '🙈 Hidden'}
                </div>
                <div class="${dashboardVisible ? 'success' : 'error'}">
                    Dashboard: ${dashboardVisible ? '👁️ Visible' : '🙈 Hidden'}
                </div>
            `;
        }

        function checkElements() {
            const elementsDiv = document.getElementById('elementsInfo');
            const loginScreen = document.getElementById('loginScreen');
            const dashboard = document.getElementById('dashboard');
            const usernameInput = document.getElementById('username');
            const passwordInput = document.getElementById('password');
            
            log('Checking DOM elements...');
            
            elementsDiv.innerHTML = `
                <pre>
loginScreen element: ${loginScreen ? '✅ Found' : '❌ Missing'}
  - Classes: ${loginScreen ? loginScreen.className : 'N/A'}
  - Display: ${loginScreen ? getComputedStyle(loginScreen).display : 'N/A'}

dashboard element: ${dashboard ? '✅ Found' : '❌ Missing'}
  - Classes: ${dashboard ? dashboard.className : 'N/A'}
  - Display: ${dashboard ? getComputedStyle(dashboard).display : 'N/A'}

username input: ${usernameInput ? '✅ Found' : '❌ Missing'}
password input: ${passwordInput ? '✅ Found' : '❌ Missing'}
                </pre>
            `;
        }

        function showDashboard() {
            log('showDashboard() called');
            
            const loginScreen = document.getElementById('loginScreen');
            const dashboard = document.getElementById('dashboard');
            
            if (loginScreen) {
                loginScreen.classList.add('hidden');
                log('Login screen hidden');
            }
            
            if (dashboard) {
                dashboard.classList.remove('hidden');
                log('Dashboard shown');
            }
            
            updateStatus();
        }

        function showLoginForm() {
            log('showLoginForm() called');
            
            const loginScreen = document.getElementById('loginScreen');
            const dashboard = document.getElementById('dashboard');
            
            if (loginScreen) {
                loginScreen.classList.remove('hidden');
                log('Login screen shown');
            }
            
            if (dashboard) {
                dashboard.classList.add('hidden');
                log('Dashboard hidden');
            }
            
            updateStatus();
        }

        async function testLogin() {
            log('Testing login API...');
            const authDiv = document.getElementById('authInfo');
            
            try {
                const response = await fetch('/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'Badboyztv', password: 'Skyblue14!' })
                });
                
                const data = await response.json();
                log(`Login API response: ${response.status} - ${JSON.stringify(data)}`);
                
                if (response.ok && data.success) {
                    authToken = data.data.token;
                    localStorage.setItem('authToken', authToken);
                    log('Token stored successfully');
                    
                    authDiv.innerHTML = `<div class="success">✅ Login successful! Token: ${authToken.substring(0, 20)}...</div>`;
                } else {
                    authDiv.innerHTML = `<div class="error">❌ Login failed: ${data.message || 'Unknown error'}</div>`;
                }
            } catch (error) {
                log(`Login error: ${error.message}`);
                authDiv.innerHTML = `<div class="error">❌ Login error: ${error.message}</div>`;
            }
            
            updateStatus();
        }

        function testDashboardShow() {
            log('Force showing dashboard...');
            showDashboard();
        }

        function testLoginShow() {
            log('Force showing login...');
            showLoginForm();
        }

        function simulateLogin() {
            log('Simulating login process...');
            
            // Simulate successful login
            authToken = 'simulated_token_' + Date.now();
            localStorage.setItem('authToken', authToken);
            
            log('Simulated token created: ' + authToken);
            
            // Show dashboard after delay
            setTimeout(() => {
                showDashboard();
                log('Dashboard should now be visible');
            }, 500);
        }

        // Initialize
        window.addEventListener('load', function() {
            log('Debug page loaded');
            updateStatus();
            checkElements();
        });
    </script>
</body>
</html>
