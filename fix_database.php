<?php
/**
 * Fix Database Schema - Add missing columns
 * Run this to add missing auth_token and token_expires columns
 */

require_once 'config/database.php';

try {
    $db = getDB();
    
    echo "<h2>🔧 Database Schema Fix</h2>";
    
    // Check if auth_token column exists
    $stmt = $db->query("SHOW COLUMNS FROM reseller_admins LIKE 'auth_token'");
    $authTokenExists = $stmt->fetch();
    
    if (!$authTokenExists) {
        echo "Adding auth_token column...<br>";
        $db->exec("ALTER TABLE reseller_admins ADD COLUMN auth_token VARCHAR(255) NULL DEFAULT NULL AFTER locked_until");
        echo "✅ auth_token column added<br>";
    } else {
        echo "✅ auth_token column already exists<br>";
    }
    
    // Check if token_expires column exists
    $stmt = $db->query("SHOW COLUMNS FROM reseller_admins LIKE 'token_expires'");
    $tokenExpiresExists = $stmt->fetch();
    
    if (!$tokenExpiresExists) {
        echo "Adding token_expires column...<br>";
        $db->exec("ALTER TABLE reseller_admins ADD COLUMN token_expires TIMESTAMP NULL DEFAULT NULL AFTER auth_token");
        echo "✅ token_expires column added<br>";
    } else {
        echo "✅ token_expires column already exists<br>";
    }
    
    // Now fix the admin user
    echo "<hr>";
    echo "<h3>🔑 Setting up admin user...</h3>";
    
    $passwordHash = hashPassword('Skyblue14!');
    
    // Check if admin exists
    $stmt = $db->prepare("SELECT id FROM reseller_admins WHERE username = ?");
    $stmt->execute(['Badboyztv']);
    $existing = $stmt->fetch();
    
    if ($existing) {
        // Update existing admin
        $stmt = $db->prepare("
            UPDATE reseller_admins 
            SET password_hash = ?, auth_token = NULL, token_expires = NULL, status = 'active'
            WHERE username = ?
        ");
        $result = $stmt->execute([$passwordHash, 'Badboyztv']);
        echo "✅ Admin user updated<br>";
    } else {
        // Create new admin
        $stmt = $db->prepare("
            INSERT INTO reseller_admins (username, password_hash, email, full_name, role, status)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $result = $stmt->execute([
            'Badboyztv',
            $passwordHash,
            '<EMAIL>',
            'BadBoyz Admin',
            'admin',
            'active'
        ]);
        echo "✅ Admin user created<br>";
    }
    
    // Test password verification
    $stmt = $db->prepare("SELECT password_hash FROM reseller_admins WHERE username = ?");
    $stmt->execute(['Badboyztv']);
    $admin = $stmt->fetch();
    
    if ($admin && verifyPassword('Skyblue14!', $admin['password_hash'])) {
        echo "✅ Password verification: <strong>PASSED</strong><br>";
    } else {
        echo "❌ Password verification: <strong>FAILED</strong><br>";
    }
    
    echo "<hr>";
    echo "<h3>✅ Database fix completed!</h3>";
    echo "<p><strong>Login credentials:</strong></p>";
    echo "<p>Username: <strong>Badboyztv</strong></p>";
    echo "<p>Password: <strong>Skyblue14!</strong></p>";
    echo "<p><a href='emergency_login.php'>🚨 Try Emergency Login</a></p>";
    echo "<p><a href='debug_auth.php'>🔍 Debug Authentication</a></p>";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage();
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2, h3 { color: #333; }
hr { margin: 20px 0; }
strong { color: #d63384; }
a { color: #0d6efd; text-decoration: none; padding: 5px 10px; background: #f8f9fa; border-radius: 4px; }
</style>
