<?php
/**
 * Authentication Debug Tool
 * Visit: https://badboyzmedia.org/debug_auth.php
 */

// Start session
session_start();

?>
<!DOCTYPE html>
<html>
<head>
    <title>Authentication Debug - BadBoyz IPTV</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .success { background: #d4edda; padding: 15px; border-radius: 4px; margin: 10px 0; color: #155724; }
        .error { background: #f8d7da; padding: 15px; border-radius: 4px; margin: 10px 0; color: #721c24; }
        .info { background: #d1ecf1; padding: 15px; border-radius: 4px; margin: 10px 0; color: #0c5460; }
        .warning { background: #fff3cd; padding: 15px; border-radius: 4px; margin: 10px 0; color: #856404; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        #results { margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Authentication Debug Tool</h1>
        
        <div class="info">
            <h3>Current Session Status:</h3>
            <pre><?php echo json_encode($_SESSION, JSON_PRETTY_PRINT); ?></pre>
        </div>

        <div class="warning">
            <h3>Instructions:</h3>
            <ol>
                <li>First, login using the emergency login</li>
                <li>Then come back to this page and test the API</li>
                <li>Check browser console for detailed logs</li>
            </ol>
        </div>

        <div>
            <button class="btn-primary" onclick="testLocalStorage()">🔍 Check localStorage</button>
            <button class="btn-success" onclick="testDebugAPI()">🔧 Test Debug API</button>
            <button class="btn-success" onclick="testAuthAPI()">🔐 Test Auth API</button>
            <button class="btn-success" onclick="testDashboardAPI()">📊 Test Dashboard API</button>
            <button class="btn-danger" onclick="clearResults()">🗑️ Clear Results</button>
        </div>

        <div id="results"></div>

        <div class="info">
            <h3>Quick Links:</h3>
            <p><a href="emergency_login.php" target="_blank">🚨 Emergency Login</a></p>
            <p><a href="reseller-panel.html" target="_blank">📊 Reseller Panel</a></p>
        </div>
    </div>

    <script>
        function addResult(title, content, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `<h4>${title}</h4><pre>${JSON.stringify(content, null, 2)}</pre>`;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function testLocalStorage() {
            console.log('=== localStorage Test ===');
            const token = localStorage.getItem('authToken');
            const result = {
                hasToken: !!token,
                tokenLength: token ? token.length : 0,
                tokenPreview: token ? token.substring(0, 20) + '...' : 'No token',
                allLocalStorage: {...localStorage}
            };
            console.log('localStorage result:', result);
            addResult('📦 localStorage Test', result, token ? 'success' : 'error');
        }

        async function testDebugAPI() {
            console.log('=== Debug API Test ===');
            try {
                const token = localStorage.getItem('authToken');
                const response = await fetch('/api/debug', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': token ? `Bearer ${token}` : ''
                    }
                });
                
                const data = await response.json();
                console.log('Debug API response:', data);
                addResult('🔧 Debug API Test', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, response.ok ? 'success' : 'error');
            } catch (error) {
                console.error('Debug API error:', error);
                addResult('🔧 Debug API Test', {error: error.message}, 'error');
            }
        }

        async function testAuthAPI() {
            console.log('=== Auth API Test ===');
            try {
                const token = localStorage.getItem('authToken');
                const response = await fetch('/api/auth/verify', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': token ? `Bearer ${token}` : ''
                    }
                });
                
                const data = await response.json();
                console.log('Auth API response:', data);
                addResult('🔐 Auth API Test', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, response.ok ? 'success' : 'error');
            } catch (error) {
                console.error('Auth API error:', error);
                addResult('🔐 Auth API Test', {error: error.message}, 'error');
            }
        }

        async function testDashboardAPI() {
            console.log('=== Dashboard API Test ===');
            try {
                const token = localStorage.getItem('authToken');
                const response = await fetch('/api/dashboard', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': token ? `Bearer ${token}` : ''
                    }
                });
                
                const data = await response.json();
                console.log('Dashboard API response:', data);
                addResult('📊 Dashboard API Test', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, response.ok ? 'success' : 'error');
            } catch (error) {
                console.error('Dashboard API error:', error);
                addResult('📊 Dashboard API Test', {error: error.message}, 'error');
            }
        }

        // Auto-test on page load
        window.addEventListener('load', function() {
            console.log('Page loaded, running auto-tests...');
            testLocalStorage();
        });
    </script>
</body>
</html>
