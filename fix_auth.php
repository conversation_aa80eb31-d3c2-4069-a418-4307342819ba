<?php
/**
 * Authentication Fix - Test and fix session issues
 * Visit: https://badboyzmedia.org/fix_auth.php
 * DELETE this file after testing!
 */

// Configure session settings
ini_set('session.cookie_httponly', 1);
ini_set('session.use_strict_mode', 1);
ini_set('session.cookie_samesite', 'Lax');

session_start();

echo "<h2>🔧 Authentication Fix</h2>";

// Test 1: Check if we can login and maintain session
if (isset($_POST['action']) && $_POST['action'] === 'login') {
    require_once 'config/database.php';
    
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    if ($username === 'Badboyztv' && $password === 'Skyblue14!') {
        // Simulate successful login
        $token = 'fixed_token_' . time();
        $_SESSION['admin_id'] = 1;
        $_SESSION['username'] = $username;
        $_SESSION['role'] = 'admin';
        $_SESSION['token'] = $token;
        $_SESSION['login_time'] = time();
        
        echo "<div style='background:#d4edda;padding:10px;border-radius:4px;margin:10px 0;'>";
        echo "✅ Login successful! Session data set.<br>";
        echo "Token: $token<br>";
        echo "Session ID: " . session_id() . "<br>";
        echo "</div>";
        
        // Test immediate verification
        echo "<h3>Testing immediate verification:</h3>";
        if (isset($_SESSION['admin_id']) && isset($_SESSION['token'])) {
            echo "<div style='background:#d4edda;padding:10px;border-radius:4px;margin:10px 0;'>";
            echo "✅ Session data available immediately<br>";
            echo "Admin ID: " . $_SESSION['admin_id'] . "<br>";
            echo "Token: " . $_SESSION['token'] . "<br>";
            echo "</div>";
        } else {
            echo "<div style='background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;'>";
            echo "❌ Session data not available immediately<br>";
            echo "</div>";
        }
    } else {
        echo "<div style='background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;'>";
        echo "❌ Invalid credentials<br>";
        echo "</div>";
    }
}

// Test 2: Check current session
echo "<h3>Current Session Status:</h3>";
if (empty($_SESSION)) {
    echo "<p>❌ No session data</p>";
} else {
    echo "<div style='background:#d1ecf1;padding:10px;border-radius:4px;margin:10px 0;'>";
    echo "<pre>" . json_encode($_SESSION, JSON_PRETTY_PRINT) . "</pre>";
    echo "</div>";
}

// Test 3: Test API call with session
if (isset($_SESSION['token'])) {
    echo "<h3>Testing API Call:</h3>";
    
    $token = $_SESSION['token'];
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://badboyzmedia.org/api/auth/verify');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json',
        'Authorization: Bearer ' . $token
    ]);
    
    // Include session cookie
    $sessionName = session_name();
    $sessionId = session_id();
    curl_setopt($ch, CURLOPT_COOKIE, "$sessionName=$sessionId");
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "<p>API Response (HTTP $httpCode):</p>";
    if ($httpCode === 200) {
        echo "<div style='background:#d4edda;padding:10px;border-radius:4px;margin:10px 0;'>";
        echo "✅ API authentication successful!<br>";
        $data = json_decode($response, true);
        if ($data) {
            echo "<pre>" . json_encode($data, JSON_PRETTY_PRINT) . "</pre>";
        }
        echo "</div>";
    } else {
        echo "<div style='background:#f8d7da;padding:10px;border-radius:4px;margin:10px 0;'>";
        echo "❌ API authentication failed<br>";
        echo "Response: " . htmlspecialchars($response) . "<br>";
        echo "</div>";
    }
}

// Login form
if (!isset($_SESSION['admin_id'])) {
    echo "<h3>Test Login:</h3>";
    echo "<form method='post'>";
    echo "<input type='hidden' name='action' value='login'>";
    echo "<p>Username: <input type='text' name='username' value='Badboyztv'></p>";
    echo "<p>Password: <input type='password' name='password' value='Skyblue14!'></p>";
    echo "<p><button type='submit'>Test Login</button></p>";
    echo "</form>";
} else {
    echo "<h3>Actions:</h3>";
    echo "<p><a href='?logout=1'>Logout</a> | <a href='?'>Refresh</a></p>";
}

// Logout
if (isset($_GET['logout'])) {
    session_destroy();
    echo "<script>window.location.href = '?';</script>";
}

echo "<hr>";
echo "<h3>📋 Instructions:</h3>";
echo "<ol>";
echo "<li>Click 'Test Login' to simulate login</li>";
echo "<li>Check if session data persists on refresh</li>";
echo "<li>Check if API call succeeds with session</li>";
echo "<li>If API fails, there's a session sharing issue</li>";
echo "</ol>";
echo "<p>🔒 <strong>DELETE this file for security!</strong></p>";

echo "<style>body{font-family:Arial,sans-serif;margin:20px;} h2{color:#333;} h3{color:#666;border-bottom:1px solid #eee;} pre{background:#f5f5f5;padding:10px;border-radius:4px;overflow-x:auto;} form{background:#f8f9fa;padding:15px;border-radius:4px;} input,button{padding:8px;margin:5px;}</style>";
?>
