<?php
/**
 * Simple Server Test
 * Visit: https://badboyzmedia.org/test.php
 */

echo "<h1>✅ Server is Working!</h1>";
echo "<p>PHP Version: " . PHP_VERSION . "</p>";
echo "<p>Server Time: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>Server Software: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "</p>";

// Test database connection
echo "<h2>Database Test:</h2>";
try {
    require_once 'config/database.php';
    $db = getDB();
    echo "<p>✅ Database connection successful!</p>";
    
    // Test admin table
    $stmt = $db->query("SELECT COUNT(*) as count FROM reseller_admins");
    $result = $stmt->fetch();
    echo "<p>📊 Admin count: " . $result['count'] . "</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Database error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>Next Steps:</h2>";
echo "<ol>";
echo "<li>If you see this page, the server is working</li>";
echo "<li>Try accessing: <a href='reseller-panel.html'>reseller-panel.html</a></li>";
echo "<li>Try accessing: <a href='simple_login.php'>simple_login.php</a></li>";
echo "<li>If those work, the 500 error is fixed</li>";
echo "</ol>";

echo "<p>🔒 <strong>DELETE this file after testing!</strong></p>";
?>
