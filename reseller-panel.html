<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BadBoyz IPTV - Reseller Panel</title>
    <link rel="icon" type="image/x-icon" href="new.jpg">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="reseller-styles.css">
</head>
<body>
    <!-- Login Screen -->
    <div id="loginScreen" class="login-screen">
        <div class="login-container">
            <div class="login-header">
                <div class="logo">
                    <img src="new.jpg" alt="BadBoyz IPTV" class="logo-image">
                    <span>BadBoyz IPTV</span>
                </div>
                <h2>Reseller Panel</h2>
                <p>Access your reseller dashboard</p>
            </div>
            
            <form id="loginForm" class="login-form">
                <div class="form-group">
                    <label for="username">Username</label>
                    <input type="text" id="username" name="username" required>
                    <i class="fas fa-user"></i>
                </div>
                
                <div class="form-group">
                    <label for="password">Password</label>
                    <input type="password" id="password" name="password" required>
                    <i class="fas fa-lock"></i>
                </div>
                
                <button type="submit" class="login-btn">
                    <i class="fas fa-sign-in-alt"></i>
                    Login to Panel
                </button>
                
                <div class="login-links">
                    <a href="#" onclick="showForgotPassword()">Forgot Password?</a>
                    <a href="#" onclick="showContactSupport()">Need Help?</a>
                </div>
            </form>
        </div>
    </div>

    <!-- Main Dashboard -->
    <div id="dashboard" class="dashboard hidden">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <img src="new.jpg" alt="BadBoyz IPTV" class="logo-image">
                    <span>Reseller Panel</span>
                </div>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="#" onclick="showSection('overview')" class="nav-link active">
                        <i class="fas fa-chart-line"></i> Overview
                    </a></li>
                    <li><a href="#" onclick="showSection('customers')" class="nav-link">
                        <i class="fas fa-users"></i> Customers
                    </a></li>
                    <li><a href="#" onclick="showSection('subscriptions')" class="nav-link">
                        <i class="fas fa-tv"></i> Subscriptions
                    </a></li>
                    <li><a href="#" onclick="showSection('sales')" class="nav-link">
                        <i class="fas fa-dollar-sign"></i> Sales & Earnings
                    </a></li>
                    <li><a href="#" onclick="showSection('credits')" class="nav-link">
                        <i class="fas fa-coins"></i> Credits
                    </a></li>
                    <li><a href="#" onclick="showSection('tools')" class="nav-link">
                        <i class="fas fa-tools"></i> Tools
                    </a></li>
                    <li><a href="#" onclick="showSection('support')" class="nav-link">
                        <i class="fas fa-headset"></i> Support
                    </a></li>
                    <li><a href="#" onclick="showSection('settings')" class="nav-link">
                        <i class="fas fa-cog"></i> Settings
                    </a></li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <div class="user-info">
                    <div class="user-avatar">
                        <i class="fas fa-user-circle"></i>
                    </div>
                    <div class="user-details">
                        <span class="user-name" id="userName">John Doe</span>
                        <span class="user-role">Reseller</span>
                    </div>
                </div>
                <button onclick="logout()" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                </button>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="content-header">
                <div class="header-left">
                    <button class="sidebar-toggle" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 id="pageTitle">Dashboard Overview</h1>
                </div>
                
                <div class="header-right">
                    <div class="notifications">
                        <button class="notification-btn">
                            <i class="fas fa-bell"></i>
                            <span class="notification-badge">3</span>
                        </button>
                    </div>
                    
                    <div class="quick-actions">
                        <button class="btn btn-primary" onclick="showAddCustomerModal()">
                            <i class="fas fa-plus"></i> Add Customer
                        </button>
                    </div>
                </div>
            </header>

            <!-- Content Sections -->
            <div class="content-body">
                <!-- Overview Section -->
                <section id="overviewSection" class="content-section active">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-content">
                                <h3>Total Customers</h3>
                                <p class="stat-number">247</p>
                                <span class="stat-change positive">+12 this month</span>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-tv"></i>
                            </div>
                            <div class="stat-content">
                                <h3>Active Subscriptions</h3>
                                <p class="stat-number">189</p>
                                <span class="stat-change positive">+8 this week</span>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="stat-content">
                                <h3>Monthly Earnings</h3>
                                <p class="stat-number">$2,847</p>
                                <span class="stat-change positive">+15.3%</span>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-coins"></i>
                            </div>
                            <div class="stat-content">
                                <h3>Available Credits</h3>
                                <p class="stat-number">156</p>
                                <span class="stat-change neutral">Buy more</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="dashboard-grid">
                        <div class="dashboard-card">
                            <h3>Recent Sales</h3>
                            <div class="recent-sales">
                                <div class="sale-item">
                                    <div class="sale-info">
                                        <span class="customer-name">Mike Johnson</span>
                                        <span class="sale-plan">Premium Plan</span>
                                    </div>
                                    <div class="sale-amount">$35</div>
                                </div>
                                <div class="sale-item">
                                    <div class="sale-info">
                                        <span class="customer-name">Sarah Wilson</span>
                                        <span class="sale-plan">Standard Plan</span>
                                    </div>
                                    <div class="sale-amount">$30</div>
                                </div>
                                <div class="sale-item">
                                    <div class="sale-info">
                                        <span class="customer-name">David Brown</span>
                                        <span class="sale-plan">Basic Plan</span>
                                    </div>
                                    <div class="sale-amount">$25</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="dashboard-card">
                            <h3>Quick Actions</h3>
                            <div class="quick-actions-grid">
                                <button class="action-btn" onclick="showAddCustomerModal()">
                                    <i class="fas fa-user-plus"></i>
                                    <span>Add Customer</span>
                                </button>
                                <button class="action-btn" onclick="showSection('credits')">
                                    <i class="fas fa-shopping-cart"></i>
                                    <span>Buy Credits</span>
                                </button>
                                <button class="action-btn" onclick="showSection('tools')">
                                    <i class="fas fa-download"></i>
                                    <span>Generate Links</span>
                                </button>
                                <button class="action-btn" onclick="showSection('support')">
                                    <i class="fas fa-headset"></i>
                                    <span>Contact Support</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Customers Section -->
                <section id="customersSection" class="content-section">
                    <div class="section-header">
                        <h2>Customer Management</h2>
                        <div class="header-actions">
                            <button class="btn btn-success" onclick="showRegisterUserModal()">
                                <i class="fas fa-user-plus"></i> Register New User
                            </button>
                            <button class="btn btn-primary" onclick="showAddCustomerModal()">
                                <i class="fas fa-plus"></i> Add Existing Customer
                            </button>
                        </div>
                    </div>
                    <div class="customers-table-container">
                        <table class="customers-table">
                            <thead>
                                <tr>
                                    <th>Customer</th>
                                    <th>Plan</th>
                                    <th>Status</th>
                                    <th>Due Date</th>
                                    <th>Payments</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="customersTableBody">
                                <!-- Customer data will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- Sales Section -->
                <section id="salesSection" class="content-section">
                    <div class="section-header">
                        <h2>Sales & Earnings</h2>
                    </div>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="stat-content">
                                <h3>This Month</h3>
                                <p class="stat-number">$2,847</p>
                                <span class="stat-change positive">+15.3% vs last month</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="stat-content">
                                <h3>This Year</h3>
                                <p class="stat-number">$28,450</p>
                                <span class="stat-change positive">+22.1% vs last year</span>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Credits Section -->
                <section id="creditsSection" class="content-section">
                    <div class="section-header">
                        <h2>Credit Management</h2>
                    </div>
                    <div class="dashboard-grid">
                        <div class="dashboard-card">
                            <h3>Available Credits</h3>
                            <div style="text-align: center; padding: 2rem;">
                                <div style="font-size: 3rem; font-weight: 700; color: var(--primary-color); margin-bottom: 1rem;">156</div>
                                <p style="color: var(--text-secondary); margin-bottom: 2rem;">Credits remaining</p>
                                <button class="btn btn-primary">
                                    <i class="fas fa-shopping-cart"></i> Buy More Credits
                                </button>
                            </div>
                        </div>
                        <div class="dashboard-card">
                            <h3>Credit Packages</h3>
                            <div style="display: flex; flex-direction: column; gap: 1rem;">
                                <div style="padding: 1rem; border: 2px solid var(--border-color); border-radius: var(--border-radius);">
                                    <strong>50 Credits - $100</strong><br>
                                    <small style="color: var(--text-secondary);">$2.00 per credit</small>
                                </div>
                                <div style="padding: 1rem; border: 2px solid var(--primary-color); border-radius: var(--border-radius); background: rgba(255, 107, 53, 0.1);">
                                    <strong>100 Credits - $180</strong> <span style="color: var(--success-color);">BEST VALUE</span><br>
                                    <small style="color: var(--text-secondary);">$1.80 per credit</small>
                                </div>
                                <div style="padding: 1rem; border: 2px solid var(--border-color); border-radius: var(--border-radius);">
                                    <strong>200 Credits - $320</strong><br>
                                    <small style="color: var(--text-secondary);">$1.60 per credit</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Tools Section -->
                <section id="toolsSection" class="content-section">
                    <div class="section-header">
                        <h2>Reseller Tools</h2>
                    </div>
                    <div class="dashboard-grid">
                        <div class="dashboard-card">
                            <h3>Generate Customer Links</h3>
                            <div class="form-group">
                                <label>Select Plan</label>
                                <select id="linkPlan">
                                    <option value="basic">Basic - $25</option>
                                    <option value="standard">Standard - $30</option>
                                    <option value="premium">Premium - $35</option>
                                </select>
                            </div>
                            <button class="btn btn-primary" onclick="generateLink()">
                                <i class="fas fa-link"></i> Generate Link
                            </button>
                            <div id="generatedLink" style="margin-top: 1rem; display: none;">
                                <label>Generated Link:</label>
                                <input type="text" id="linkOutput" readonly style="margin-top: 0.5rem;">
                                <button class="btn btn-secondary" onclick="copyLink()" style="margin-top: 0.5rem;">
                                    <i class="fas fa-copy"></i> Copy Link
                                </button>
                            </div>
                        </div>
                        <div class="dashboard-card">
                            <h3>Download Resources</h3>
                            <div style="display: flex; flex-direction: column; gap: 1rem;">
                                <button class="btn btn-secondary">
                                    <i class="fas fa-download"></i> Download APK Files
                                </button>
                                <button class="btn btn-secondary">
                                    <i class="fas fa-download"></i> Setup Guides
                                </button>
                                <button class="btn btn-secondary">
                                    <i class="fas fa-download"></i> Marketing Materials
                                </button>
                                <button class="btn btn-secondary">
                                    <i class="fas fa-download"></i> Price Lists
                                </button>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Support Section -->
                <section id="supportSection" class="content-section">
                    <div class="section-header">
                        <h2>Support Center</h2>
                    </div>
                    <div class="dashboard-grid">
                        <div class="dashboard-card">
                            <h3>Contact Support</h3>
                            <div style="display: flex; flex-direction: column; gap: 1rem;">
                                <div style="display: flex; align-items: center; gap: 1rem; padding: 1rem; background: var(--bg-secondary); border-radius: var(--border-radius);">
                                    <i class="fab fa-telegram" style="font-size: 1.5rem; color: var(--accent-color);"></i>
                                    <div>
                                        <strong>Telegram Support</strong><br>
                                        <small style="color: var(--text-secondary);">@badboyz_reseller_support</small>
                                    </div>
                                </div>
                                <div style="display: flex; align-items: center; gap: 1rem; padding: 1rem; background: var(--bg-secondary); border-radius: var(--border-radius);">
                                    <i class="fas fa-envelope" style="font-size: 1.5rem; color: var(--primary-color);"></i>
                                    <div>
                                        <strong>Email Support</strong><br>
                                        <small style="color: var(--text-secondary);"><EMAIL></small>
                                    </div>
                                </div>
                                <div style="display: flex; align-items: center; gap: 1rem; padding: 1rem; background: var(--bg-secondary); border-radius: var(--border-radius);">
                                    <i class="fas fa-phone" style="font-size: 1.5rem; color: var(--success-color);"></i>
                                    <div>
                                        <strong>Phone Support</strong><br>
                                        <small style="color: var(--text-secondary);">+1-XXX-XXX-XXXX</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="dashboard-card">
                            <h3>Quick Help</h3>
                            <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                                <a href="#" style="color: var(--primary-color); text-decoration: none; padding: 0.5rem 0;">
                                    <i class="fas fa-question-circle"></i> How to add customers?
                                </a>
                                <a href="#" style="color: var(--primary-color); text-decoration: none; padding: 0.5rem 0;">
                                    <i class="fas fa-question-circle"></i> How to buy credits?
                                </a>
                                <a href="#" style="color: var(--primary-color); text-decoration: none; padding: 0.5rem 0;">
                                    <i class="fas fa-question-circle"></i> How to generate links?
                                </a>
                                <a href="#" style="color: var(--primary-color); text-decoration: none; padding: 0.5rem 0;">
                                    <i class="fas fa-question-circle"></i> Commission structure
                                </a>
                                <a href="#" style="color: var(--primary-color); text-decoration: none; padding: 0.5rem 0;">
                                    <i class="fas fa-question-circle"></i> Payment methods
                                </a>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Subscriptions Section -->
                <section id="subscriptionsSection" class="content-section">
                    <div class="section-header">
                        <h2>Subscription Management</h2>
                        <button class="btn btn-primary" onclick="showBulkActionsModal()">
                            <i class="fas fa-tasks"></i> Bulk Actions
                        </button>
                    </div>

                    <div class="subscription-filters">
                        <div class="filter-group">
                            <label>Filter by Status:</label>
                            <select id="statusFilter" onchange="filterSubscriptions()">
                                <option value="all">All Subscriptions</option>
                                <option value="active">Active</option>
                                <option value="expired">Expired</option>
                                <option value="pending">Pending</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>Filter by Plan:</label>
                            <select id="planFilter" onchange="filterSubscriptions()">
                                <option value="all">All Plans</option>
                                <option value="basic">Basic</option>
                                <option value="standard">Standard</option>
                                <option value="premium">Premium</option>
                            </select>
                        </div>
                    </div>

                    <div class="subscriptions-table-container">
                        <table class="customers-table">
                            <thead>
                                <tr>
                                    <th><input type="checkbox" id="selectAll" onchange="toggleSelectAll()"></th>
                                    <th>Customer</th>
                                    <th>Plan</th>
                                    <th>Status</th>
                                    <th>Start Date</th>
                                    <th>Expires</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="subscriptionsTableBody">
                                <!-- Subscription data will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </section>

                <!-- Settings Section -->
                <section id="settingsSection" class="content-section">
                    <div class="section-header">
                        <h2>Account Settings</h2>
                    </div>

                    <div class="settings-grid">
                        <div class="settings-card">
                            <h3>Profile Information</h3>
                            <form id="profileForm" class="settings-form">
                                <div class="form-group">
                                    <label for="profileName">Full Name</label>
                                    <input type="text" id="profileName" value="John Doe">
                                </div>
                                <div class="form-group">
                                    <label for="profileEmail">Email Address</label>
                                    <input type="email" id="profileEmail" value="<EMAIL>">
                                </div>
                                <div class="form-group">
                                    <label for="profilePhone">Phone Number</label>
                                    <input type="tel" id="profilePhone" value="******-0123">
                                </div>
                                <div class="form-group">
                                    <label for="profileCompany">Company Name</label>
                                    <input type="text" id="profileCompany" value="IPTV Solutions Inc.">
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Save Changes
                                </button>
                            </form>
                        </div>

                        <div class="settings-card">
                            <h3>Security Settings</h3>
                            <form id="securityForm" class="settings-form">
                                <div class="form-group">
                                    <label for="currentPassword">Current Password</label>
                                    <input type="password" id="currentPassword">
                                </div>
                                <div class="form-group">
                                    <label for="newPassword">New Password</label>
                                    <input type="password" id="newPassword">
                                </div>
                                <div class="form-group">
                                    <label for="confirmPassword">Confirm New Password</label>
                                    <input type="password" id="confirmPassword">
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-lock"></i> Update Password
                                </button>
                            </form>
                        </div>

                        <div class="settings-card">
                            <h3>Notification Preferences</h3>
                            <div class="settings-form">
                                <div class="checkbox-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" checked>
                                        <span class="checkmark"></span>
                                        Email notifications for new sales
                                    </label>
                                </div>
                                <div class="checkbox-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" checked>
                                        <span class="checkmark"></span>
                                        SMS alerts for expired subscriptions
                                    </label>
                                </div>
                                <div class="checkbox-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox">
                                        <span class="checkmark"></span>
                                        Weekly earnings reports
                                    </label>
                                </div>
                                <div class="checkbox-group">
                                    <label class="checkbox-label">
                                        <input type="checkbox" checked>
                                        <span class="checkmark"></span>
                                        Marketing updates and promotions
                                    </label>
                                </div>
                                <button class="btn btn-primary">
                                    <i class="fas fa-bell"></i> Save Preferences
                                </button>
                            </div>
                        </div>

                        <div class="settings-card">
                            <h3>Payment Settings</h3>
                            <div class="settings-form">
                                <div class="form-group">
                                    <label for="paymentMethod">Preferred Payment Method</label>
                                    <select id="paymentMethod">
                                        <option value="paypal">PayPal</option>
                                        <option value="bank">Bank Transfer</option>
                                        <option value="crypto">Cryptocurrency</option>
                                        <option value="stripe">Credit Card (Stripe)</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="paymentEmail">Payment Email/Address</label>
                                    <input type="text" id="paymentEmail" value="<EMAIL>">
                                </div>
                                <div class="form-group">
                                    <label for="minPayout">Minimum Payout Amount</label>
                                    <select id="minPayout">
                                        <option value="50">$50</option>
                                        <option value="100" selected>$100</option>
                                        <option value="200">$200</option>
                                        <option value="500">$500</option>
                                    </select>
                                </div>
                                <button class="btn btn-primary">
                                    <i class="fas fa-credit-card"></i> Update Payment Info
                                </button>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </main>
    </div>

    <!-- Modals -->
    <div id="addCustomerModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add New Customer</h3>
                <button class="modal-close" onclick="closeModal('addCustomerModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="addCustomerForm" class="modal-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="customerName">Customer Name</label>
                        <input type="text" id="customerName" required>
                    </div>
                    <div class="form-group">
                        <label for="customerEmail">Email</label>
                        <input type="email" id="customerEmail" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="customerPlan">Plan</label>
                        <select id="customerPlan" required>
                            <option value="">Select Plan</option>
                            <option value="basic">Basic - $25</option>
                            <option value="standard">Standard - $30</option>
                            <option value="premium">Premium - $35</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="customerDuration">Duration</label>
                        <select id="customerDuration" required>
                            <option value="1">1 Month</option>
                            <option value="3">3 Months</option>
                            <option value="6">6 Months</option>
                            <option value="12">12 Months</option>
                        </select>
                    </div>
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('addCustomerModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Customer</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Register User Modal -->
    <div id="registerUserModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Register New User</h3>
                <button class="modal-close" onclick="closeModal('registerUserModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="registerUserForm" class="modal-form">
                <div class="form-row">
                    <div class="form-group">
                        <label for="regFullName">Full Name *</label>
                        <input type="text" id="regFullName" name="fullName" required>
                    </div>
                    <div class="form-group">
                        <label for="regEmail">Email Address *</label>
                        <input type="email" id="regEmail" name="email" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="regUsername">Username *</label>
                        <input type="text" id="regUsername" name="username" required>
                    </div>
                    <div class="form-group">
                        <label for="regPhone">Phone Number</label>
                        <input type="tel" id="regPhone" name="phone">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="regPlan">Subscription Plan *</label>
                        <select id="regPlan" name="plan" required>
                            <option value="">Select Plan</option>
                            <option value="Basic">Basic - $25/month</option>
                            <option value="Standard">Standard - $30/month</option>
                            <option value="Premium">Premium - $35/month</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="regDuration">Duration *</label>
                        <select id="regDuration" name="duration" required>
                            <option value="">Select Duration</option>
                            <option value="1">1 Month</option>
                            <option value="3">3 Months</option>
                            <option value="6">6 Months</option>
                            <option value="12">12 Months</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="regPassword">Temporary Password *</label>
                        <input type="password" id="regPassword" name="password" required>
                        <small style="color: var(--text-secondary);">User can change this after first login</small>
                    </div>
                    <div class="form-group">
                        <label for="regDevices">Max Devices</label>
                        <select id="regDevices" name="devices">
                            <option value="1">1 Device</option>
                            <option value="2">2 Devices</option>
                            <option value="3">3 Devices</option>
                            <option value="4">4 Devices</option>
                            <option value="5">5 Devices</option>
                        </select>
                    </div>
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="closeModal('registerUserModal')">Cancel</button>
                    <button type="submit" class="btn btn-success">Register User</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Bulk Actions Modal -->
    <div id="bulkActionsModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Bulk Actions</h3>
                <button class="modal-close" onclick="closeModal('bulkActionsModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-form">
                <p>Select an action to perform on selected subscriptions:</p>
                <div class="bulk-actions-grid">
                    <button class="action-btn" onclick="bulkExtend()">
                        <i class="fas fa-calendar-plus"></i>
                        <span>Extend Subscriptions</span>
                    </button>
                    <button class="action-btn" onclick="bulkSuspend()">
                        <i class="fas fa-pause"></i>
                        <span>Suspend Subscriptions</span>
                    </button>
                    <button class="action-btn" onclick="bulkActivate()">
                        <i class="fas fa-play"></i>
                        <span>Activate Subscriptions</span>
                    </button>
                    <button class="action-btn" onclick="bulkDelete()">
                        <i class="fas fa-trash"></i>
                        <span>Delete Subscriptions</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Spinner -->
    <div id="loadingSpinner" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; justify-content: center; align-items: center;">
        <div style="background: white; padding: 2rem; border-radius: 8px; text-align: center;">
            <div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #007bff; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 1rem;"></div>
            <p>Loading...</p>
        </div>
    </div>

    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        #loadingSpinner[style*="block"] {
            display: flex !important;
        }
    </style>

    <script src="reseller-script-api.js"></script>
</body>
</html>
