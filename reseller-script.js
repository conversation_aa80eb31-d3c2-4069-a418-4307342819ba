// Reseller Panel JavaScript

// Authentication credentials
const ADMIN_CREDENTIALS = {
    username: '<PERSON><PERSON>ztv',
    password: '<PERSON><PERSON><PERSON><PERSON>!'
};

// Demo data with enhanced structure
const demoCustomers = [
    {
        id: 1,
        name: "<PERSON>",
        email: "<EMAIL>",
        username: "johnsmith123",
        plan: "Premium",
        status: "active",
        expires: "2025-02-15",
        devices: 3,
        registeredDate: "2024-11-15",
        lastPayment: "2025-01-15",
        totalPaid: 105,
        phone: "******-0101"
    },
    {
        id: 2,
        name: "<PERSON>",
        email: "<EMAIL>",
        username: "sarahj456",
        plan: "Standard",
        status: "active",
        expires: "2025-02-20",
        devices: 2,
        registeredDate: "2024-12-20",
        lastPayment: "2025-01-20",
        totalPaid: 60,
        phone: "******-0102"
    },
    {
        id: 3,
        name: "<PERSON>",
        email: "<EMAIL>",
        username: "mikew789",
        plan: "Basic",
        status: "expired",
        expires: "2025-01-10",
        devices: 1,
        registeredDate: "2024-10-10",
        lastPayment: "2024-12-10",
        totalPaid: 75,
        phone: "******-0103"
    },
    {
        id: 4,
        name: "Lisa <PERSON>",
        email: "<EMAIL>",
        username: "lisab321",
        plan: "Premium",
        status: "due_soon",
        expires: "2025-01-30",
        devices: 4,
        registeredDate: "2024-12-01",
        lastPayment: "2024-12-30",
        totalPaid: 140,
        phone: "******-0104"
    },
    {
        id: 5,
        name: "David Brown",
        email: "<EMAIL>",
        username: "davidb654",
        plan: "Standard",
        status: "active",
        expires: "2025-03-05",
        devices: 2,
        registeredDate: "2024-11-30",
        lastPayment: "2025-02-05",
        totalPaid: 90,
        phone: "******-0105"
    }
];

// DOM Elements
const loginScreen = document.getElementById('loginScreen');
const dashboard = document.getElementById('dashboard');
const loginForm = document.getElementById('loginForm');
const sidebar = document.querySelector('.sidebar');
const navLinks = document.querySelectorAll('.nav-link');
const contentSections = document.querySelectorAll('.content-section');
const pageTitle = document.getElementById('pageTitle');
const userName = document.getElementById('userName');

// Initialize
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is already logged in
    const isLoggedIn = localStorage.getItem('resellerLoggedIn');
    if (isLoggedIn) {
        showDashboard();
    }
    
    // Load customer data
    loadCustomers();
    
    // Set up event listeners
    setupEventListeners();
});

function setupEventListeners() {
    // Login form
    loginForm.addEventListener('submit', handleLogin);

    // Add customer form
    const addCustomerForm = document.getElementById('addCustomerForm');
    if (addCustomerForm) {
        addCustomerForm.addEventListener('submit', handleAddCustomer);
    }

    // Register user form
    const registerUserForm = document.getElementById('registerUserForm');
    if (registerUserForm) {
        registerUserForm.addEventListener('submit', handleRegisterUser);
    }

    // Settings will be set up when settings section is loaded
}

// Authentication
function handleLogin(e) {
    e.preventDefault();

    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;

    // Check credentials
    if (username === ADMIN_CREDENTIALS.username && password === ADMIN_CREDENTIALS.password) {
        localStorage.setItem('resellerLoggedIn', 'true');
        localStorage.setItem('resellerUsername', username);
        localStorage.setItem('loginTime', new Date().toISOString());
        showDashboard();
        showNotification('Welcome back, ' + username + '!', 'success');
    } else {
        showNotification('Invalid username or password. Please try again.', 'error');
        // Clear the form
        document.getElementById('username').value = '';
        document.getElementById('password').value = '';
    }
}

function showDashboard() {
    loginScreen.classList.add('hidden');
    dashboard.classList.remove('hidden');
    
    // Set username
    const storedUsername = localStorage.getItem('resellerUsername') || 'Demo User';
    userName.textContent = storedUsername;
}

function logout() {
    localStorage.removeItem('resellerLoggedIn');
    localStorage.removeItem('resellerUsername');
    dashboard.classList.add('hidden');
    loginScreen.classList.remove('hidden');
    
    // Reset form
    loginForm.reset();
}

// Navigation
function showSection(sectionName) {
    // Update active nav link
    navLinks.forEach(link => link.classList.remove('active'));
    event.target.closest('.nav-link').classList.add('active');
    
    // Hide all sections
    contentSections.forEach(section => section.classList.remove('active'));
    
    // Show selected section
    const targetSection = document.getElementById(sectionName + 'Section');
    if (targetSection) {
        targetSection.classList.add('active');
    }
    
    // Update page title
    const titles = {
        'overview': 'Dashboard Overview',
        'customers': 'Customer Management',
        'subscriptions': 'Subscription Management',
        'sales': 'Sales & Earnings',
        'credits': 'Credit Management',
        'tools': 'Reseller Tools',
        'support': 'Support Center',
        'settings': 'Account Settings'
    };
    
    pageTitle.textContent = titles[sectionName] || 'Dashboard';
    
    // Load section-specific content
    if (sectionName === 'customers') {
        loadCustomers();
    } else if (sectionName === 'subscriptions') {
        loadSubscriptions();
    } else if (sectionName === 'settings') {
        loadSettings();
    }
}

// Customer Management
function loadCustomers() {
    const tableBody = document.getElementById('customersTableBody');
    if (!tableBody) return;

    tableBody.innerHTML = '';

    demoCustomers.forEach(customer => {
        const row = document.createElement('tr');

        const statusClass = `status-${customer.status}`;
        const statusText = getStatusText(customer.status);
        const daysUntilExpiry = getDaysUntilExpiry(customer.expires);
        const expiryClass = daysUntilExpiry <= 7 ? 'text-danger' : daysUntilExpiry <= 30 ? 'text-warning' : '';

        row.innerHTML = `
            <td>
                <div>
                    <strong>${customer.name}</strong><br>
                    <small style="color: var(--text-secondary);">${customer.email}</small><br>
                    <small style="color: var(--text-secondary);">@${customer.username}</small>
                </div>
            </td>
            <td>
                <div>
                    <strong>${customer.plan}</strong><br>
                    <small style="color: var(--text-secondary);">${customer.devices} devices</small>
                </div>
            </td>
            <td><span class="status-badge ${statusClass}">${statusText}</span></td>
            <td>
                <div>
                    <strong class="${expiryClass}">${formatDate(customer.expires)}</strong><br>
                    <small style="color: var(--text-secondary);">${daysUntilExpiry > 0 ? daysUntilExpiry + ' days left' : 'Expired ' + Math.abs(daysUntilExpiry) + ' days ago'}</small>
                </div>
            </td>
            <td>
                <div>
                    <strong>$${customer.totalPaid}</strong><br>
                    <small style="color: var(--text-secondary);">Last: ${formatDate(customer.lastPayment)}</small>
                </div>
            </td>
            <td>
                <button class="btn btn-primary" onclick="editCustomer(${customer.id})" style="margin-right: 0.5rem;" title="Edit Customer">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-secondary" onclick="viewCustomer(${customer.id})" title="View Details">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-success" onclick="extendCustomer(${customer.id})" title="Extend Subscription">
                    <i class="fas fa-calendar-plus"></i>
                </button>
            </td>
        `;

        tableBody.appendChild(row);
    });

    // Update dashboard stats
    updateDashboardStats();
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function getStatusText(status) {
    const statusMap = {
        'active': 'Active',
        'expired': 'Expired',
        'due_soon': 'Due Soon',
        'pending': 'Pending',
        'suspended': 'Suspended'
    };
    return statusMap[status] || status.charAt(0).toUpperCase() + status.slice(1);
}

function getDaysUntilExpiry(expiryDate) {
    const today = new Date();
    const expiry = new Date(expiryDate);
    const diffTime = expiry - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
}

function updateDashboardStats() {
    const totalCustomers = demoCustomers.length;
    const activeCustomers = demoCustomers.filter(c => c.status === 'active').length;
    const expiredCustomers = demoCustomers.filter(c => c.status === 'expired').length;
    const dueSoonCustomers = demoCustomers.filter(c => {
        const days = getDaysUntilExpiry(c.expires);
        return days <= 7 && days > 0;
    }).length;

    // Update stats in overview section if elements exist
    const totalCustomersEl = document.getElementById('totalCustomers');
    const activeCustomersEl = document.getElementById('activeCustomers');
    const expiredCustomersEl = document.getElementById('expiredCustomers');
    const dueSoonCustomersEl = document.getElementById('dueSoonCustomers');

    if (totalCustomersEl) totalCustomersEl.textContent = totalCustomers;
    if (activeCustomersEl) activeCustomersEl.textContent = activeCustomers;
    if (expiredCustomersEl) expiredCustomersEl.textContent = expiredCustomers;
    if (dueSoonCustomersEl) dueSoonCustomersEl.textContent = dueSoonCustomers;
}

// Modal Management
function showAddCustomerModal() {
    const modal = document.getElementById('addCustomerModal');
    modal.classList.add('active');
}

function showRegisterUserModal() {
    const modal = document.getElementById('registerUserModal');
    modal.classList.add('active');
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    modal.classList.remove('active');
}

function handleAddCustomer(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const customerData = {
        id: demoCustomers.length + 1,
        name: document.getElementById('customerName').value,
        email: document.getElementById('customerEmail').value,
        plan: document.getElementById('customerPlan').value,
        duration: document.getElementById('customerDuration').value,
        status: 'active',
        expires: calculateExpiryDate(parseInt(document.getElementById('customerDuration').value)),
        devices: 0
    };
    
    // Add to demo data
    demoCustomers.push(customerData);
    
    // Reload customers table
    loadCustomers();
    
    // Close modal and reset form
    closeModal('addCustomerModal');
    e.target.reset();
    
    // Show success message
    showNotification('Customer added successfully!', 'success');
}

function calculateExpiryDate(months) {
    const date = new Date();
    date.setMonth(date.getMonth() + months);
    return date.toISOString().split('T')[0];
}

function handleRegisterUser(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const userData = {
        id: demoCustomers.length + 1,
        name: document.getElementById('regFullName').value,
        email: document.getElementById('regEmail').value,
        username: document.getElementById('regUsername').value,
        phone: document.getElementById('regPhone').value || 'Not provided',
        plan: document.getElementById('regPlan').value,
        status: 'active',
        expires: calculateExpiryDate(parseInt(document.getElementById('regDuration').value)),
        devices: parseInt(document.getElementById('regDevices').value) || 2,
        registeredDate: new Date().toISOString().split('T')[0],
        lastPayment: new Date().toISOString().split('T')[0],
        totalPaid: getPlanPrice(document.getElementById('regPlan').value) * parseInt(document.getElementById('regDuration').value),
        tempPassword: document.getElementById('regPassword').value
    };

    // Check if username or email already exists
    const existingUser = demoCustomers.find(c =>
        c.username === userData.username || c.email === userData.email
    );

    if (existingUser) {
        showNotification('Username or email already exists!', 'error');
        return;
    }

    // Add to demo data
    demoCustomers.push(userData);

    // Reload customers table
    loadCustomers();

    // Close modal and reset form
    closeModal('registerUserModal');
    e.target.reset();

    // Show success message with login details
    showNotification(`User registered successfully!
    Username: ${userData.username}
    Temp Password: ${userData.tempPassword}
    Plan: ${userData.plan} (${document.getElementById('regDuration').value} months)`, 'success');
}

function getPlanPrice(plan) {
    const prices = {
        'Basic': 25,
        'Standard': 30,
        'Premium': 35
    };
    return prices[plan] || 25;
}

// Customer Actions
function editCustomer(customerId) {
    const customer = demoCustomers.find(c => c.id === customerId);
    if (customer) {
        alert(`Edit customer: ${customer.name}\n(This would open an edit modal in a real application)`);
    }
}

function viewCustomer(customerId) {
    const customer = demoCustomers.find(c => c.id === customerId);
    if (customer) {
        const daysLeft = getDaysUntilExpiry(customer.expires);
        const statusText = getStatusText(customer.status);

        alert(`📋 CUSTOMER DETAILS

👤 Name: ${customer.name}
📧 Email: ${customer.email}
🔑 Username: ${customer.username}
📱 Phone: ${customer.phone}

📦 SUBSCRIPTION INFO
💎 Plan: ${customer.plan}
📊 Status: ${statusText}
📅 Expires: ${formatDate(customer.expires)} (${daysLeft > 0 ? daysLeft + ' days left' : 'Expired ' + Math.abs(daysLeft) + ' days ago'})
📱 Devices: ${customer.devices}

💰 PAYMENT INFO
💵 Total Paid: $${customer.totalPaid}
📅 Last Payment: ${formatDate(customer.lastPayment)}
📅 Registered: ${formatDate(customer.registeredDate)}`);
    }
}

function extendCustomer(customerId) {
    const customer = demoCustomers.find(c => c.id === customerId);
    if (customer) {
        const months = prompt('How many months to extend?', '1');
        if (months && !isNaN(months) && months > 0) {
            const currentExpiry = new Date(customer.expires);
            currentExpiry.setMonth(currentExpiry.getMonth() + parseInt(months));
            customer.expires = currentExpiry.toISOString().split('T')[0];
            customer.status = 'active';

            loadCustomers();
            showNotification(`Extended ${customer.name}'s subscription by ${months} month(s)`, 'success');
        }
    }
}

// Utility Functions
function toggleSidebar() {
    sidebar.classList.toggle('active');
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
        <span>${message}</span>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

// Support Functions
function showForgotPassword() {
    alert('Forgot Password functionality would be implemented here.\nPlease contact support for password reset.');
}

function showContactSupport() {
    alert('Support Contact:\nTelegram: @badboyz_support\nEmail: <EMAIL>\nPhone: +1-XXX-XXX-XXXX');
}

// Close modals when clicking outside
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('modal')) {
        e.target.classList.remove('active');
    }
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Escape key closes modals
    if (e.key === 'Escape') {
        const activeModal = document.querySelector('.modal.active');
        if (activeModal) {
            activeModal.classList.remove('active');
        }
    }
});

// Auto-logout after inactivity (30 minutes)
let inactivityTimer;
function resetInactivityTimer() {
    clearTimeout(inactivityTimer);
    inactivityTimer = setTimeout(() => {
        if (localStorage.getItem('resellerLoggedIn')) {
            alert('Session expired due to inactivity. Please log in again.');
            logout();
        }
    }, 30 * 60 * 1000); // 30 minutes
}

// Reset timer on user activity
document.addEventListener('mousemove', resetInactivityTimer);
document.addEventListener('keypress', resetInactivityTimer);
document.addEventListener('click', resetInactivityTimer);

// Initialize timer
resetInactivityTimer();

// Tools Functions
function generateLink() {
    const plan = document.getElementById('linkPlan').value;
    const baseUrl = window.location.origin;
    const resellerCode = 'RES' + Math.random().toString(36).substr(2, 6).toUpperCase();

    const link = `${baseUrl}/?ref=${resellerCode}&plan=${plan}`;

    document.getElementById('linkOutput').value = link;
    document.getElementById('generatedLink').style.display = 'block';

    showNotification('Link generated successfully!', 'success');
}

function copyLink() {
    const linkOutput = document.getElementById('linkOutput');
    linkOutput.select();
    linkOutput.setSelectionRange(0, 99999); // For mobile devices

    try {
        document.execCommand('copy');
        showNotification('Link copied to clipboard!', 'success');
    } catch (err) {
        showNotification('Failed to copy link', 'error');
    }
}

// Subscription Management
function loadSubscriptions() {
    const tableBody = document.getElementById('subscriptionsTableBody');
    if (!tableBody) return;

    tableBody.innerHTML = '';

    demoCustomers.forEach(customer => {
        const row = document.createElement('tr');

        const statusClass = `status-${customer.status}`;
        const statusText = customer.status.charAt(0).toUpperCase() + customer.status.slice(1);
        const startDate = new Date(customer.expires);
        startDate.setMonth(startDate.getMonth() - 1); // Assume 1 month subscription

        row.innerHTML = `
            <td><input type="checkbox" class="subscription-checkbox" value="${customer.id}"></td>
            <td>
                <div>
                    <strong>${customer.name}</strong><br>
                    <small style="color: var(--text-secondary);">${customer.email}</small>
                </div>
            </td>
            <td>${customer.plan}</td>
            <td><span class="status-badge ${statusClass}">${statusText}</span></td>
            <td>${formatDate(startDate.toISOString().split('T')[0])}</td>
            <td>${formatDate(customer.expires)}</td>
            <td>
                <button class="btn btn-primary" onclick="extendSubscription(${customer.id})" style="margin-right: 0.5rem;">
                    <i class="fas fa-calendar-plus"></i>
                </button>
                <button class="btn btn-secondary" onclick="suspendSubscription(${customer.id})">
                    <i class="fas fa-pause"></i>
                </button>
            </td>
        `;

        tableBody.appendChild(row);
    });
}

function filterSubscriptions() {
    const statusFilter = document.getElementById('statusFilter').value;
    const planFilter = document.getElementById('planFilter').value;
    const rows = document.querySelectorAll('#subscriptionsTableBody tr');

    rows.forEach(row => {
        const statusBadge = row.querySelector('.status-badge');
        const planCell = row.cells[2];

        const status = statusBadge.textContent.toLowerCase();
        const plan = planCell.textContent.toLowerCase();

        const statusMatch = statusFilter === 'all' || status === statusFilter;
        const planMatch = planFilter === 'all' || plan === planFilter;

        row.style.display = (statusMatch && planMatch) ? '' : 'none';
    });
}

function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.subscription-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

function extendSubscription(customerId) {
    const customer = demoCustomers.find(c => c.id === customerId);
    if (customer) {
        const newExpiry = new Date(customer.expires);
        newExpiry.setMonth(newExpiry.getMonth() + 1);
        customer.expires = newExpiry.toISOString().split('T')[0];

        loadSubscriptions();
        showNotification(`Extended subscription for ${customer.name}`, 'success');
    }
}

function suspendSubscription(customerId) {
    const customer = demoCustomers.find(c => c.id === customerId);
    if (customer) {
        customer.status = customer.status === 'active' ? 'expired' : 'active';

        loadSubscriptions();
        showNotification(`Subscription ${customer.status === 'active' ? 'activated' : 'suspended'} for ${customer.name}`, 'success');
    }
}

// Bulk Actions
function showBulkActionsModal() {
    const selectedCheckboxes = document.querySelectorAll('.subscription-checkbox:checked');
    if (selectedCheckboxes.length === 0) {
        showNotification('Please select at least one subscription', 'warning');
        return;
    }

    const modal = document.getElementById('bulkActionsModal');
    modal.classList.add('active');
}

function bulkExtend() {
    const selectedCheckboxes = document.querySelectorAll('.subscription-checkbox:checked');
    let count = 0;

    selectedCheckboxes.forEach(checkbox => {
        const customerId = parseInt(checkbox.value);
        const customer = demoCustomers.find(c => c.id === customerId);
        if (customer) {
            const newExpiry = new Date(customer.expires);
            newExpiry.setMonth(newExpiry.getMonth() + 1);
            customer.expires = newExpiry.toISOString().split('T')[0];
            count++;
        }
    });

    loadSubscriptions();
    closeModal('bulkActionsModal');
    showNotification(`Extended ${count} subscriptions`, 'success');
}

function bulkSuspend() {
    const selectedCheckboxes = document.querySelectorAll('.subscription-checkbox:checked');
    let count = 0;

    selectedCheckboxes.forEach(checkbox => {
        const customerId = parseInt(checkbox.value);
        const customer = demoCustomers.find(c => c.id === customerId);
        if (customer && customer.status === 'active') {
            customer.status = 'expired';
            count++;
        }
    });

    loadSubscriptions();
    closeModal('bulkActionsModal');
    showNotification(`Suspended ${count} subscriptions`, 'success');
}

function bulkActivate() {
    const selectedCheckboxes = document.querySelectorAll('.subscription-checkbox:checked');
    let count = 0;

    selectedCheckboxes.forEach(checkbox => {
        const customerId = parseInt(checkbox.value);
        const customer = demoCustomers.find(c => c.id === customerId);
        if (customer && customer.status !== 'active') {
            customer.status = 'active';
            count++;
        }
    });

    loadSubscriptions();
    closeModal('bulkActionsModal');
    showNotification(`Activated ${count} subscriptions`, 'success');
}

function bulkDelete() {
    const selectedCheckboxes = document.querySelectorAll('.subscription-checkbox:checked');

    if (confirm(`Are you sure you want to delete ${selectedCheckboxes.length} subscriptions? This action cannot be undone.`)) {
        let count = 0;

        selectedCheckboxes.forEach(checkbox => {
            const customerId = parseInt(checkbox.value);
            const index = demoCustomers.findIndex(c => c.id === customerId);
            if (index !== -1) {
                demoCustomers.splice(index, 1);
                count++;
            }
        });

        loadSubscriptions();
        closeModal('bulkActionsModal');
        showNotification(`Deleted ${count} subscriptions`, 'success');
    }
}

// Settings Management
function loadSettings() {
    // Load saved settings from localStorage or use defaults
    const savedSettings = JSON.parse(localStorage.getItem('resellerSettings')) || {};

    // Profile settings
    document.getElementById('profileName').value = savedSettings.name || 'John Doe';
    document.getElementById('profileEmail').value = savedSettings.email || '<EMAIL>';
    document.getElementById('profilePhone').value = savedSettings.phone || '******-0123';
    document.getElementById('profileCompany').value = savedSettings.company || 'IPTV Solutions Inc.';

    // Payment settings
    document.getElementById('paymentMethod').value = savedSettings.paymentMethod || 'paypal';
    document.getElementById('paymentEmail').value = savedSettings.paymentEmail || '<EMAIL>';
    document.getElementById('minPayout').value = savedSettings.minPayout || '100';

    // Set up form event listeners
    setupSettingsEventListeners();
}

function setupSettingsEventListeners() {
    // Profile form
    const profileForm = document.getElementById('profileForm');
    if (profileForm) {
        profileForm.addEventListener('submit', saveProfileSettings);
    }

    // Security form
    const securityForm = document.getElementById('securityForm');
    if (securityForm) {
        securityForm.addEventListener('submit', updatePassword);
    }
}

function saveProfileSettings(e) {
    e.preventDefault();

    const settings = {
        name: document.getElementById('profileName').value,
        email: document.getElementById('profileEmail').value,
        phone: document.getElementById('profilePhone').value,
        company: document.getElementById('profileCompany').value,
        paymentMethod: document.getElementById('paymentMethod').value,
        paymentEmail: document.getElementById('paymentEmail').value,
        minPayout: document.getElementById('minPayout').value
    };

    // Save to localStorage
    localStorage.setItem('resellerSettings', JSON.stringify(settings));

    // Update username in sidebar
    document.getElementById('userName').textContent = settings.name;

    showNotification('Profile settings saved successfully!', 'success');
}

function updatePassword(e) {
    e.preventDefault();

    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    if (!currentPassword || !newPassword || !confirmPassword) {
        showNotification('Please fill in all password fields', 'warning');
        return;
    }

    if (newPassword !== confirmPassword) {
        showNotification('New passwords do not match', 'error');
        return;
    }

    if (newPassword.length < 6) {
        showNotification('Password must be at least 6 characters long', 'error');
        return;
    }

    // In a real application, you would validate the current password
    // and update it on the server

    // Clear form
    document.getElementById('currentPassword').value = '';
    document.getElementById('newPassword').value = '';
    document.getElementById('confirmPassword').value = '';

    showNotification('Password updated successfully!', 'success');
}
