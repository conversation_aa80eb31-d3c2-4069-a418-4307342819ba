<!DOCTYPE html>
<html>
<head>
    <title>Live API Test - BadBoyz IPTV</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 900px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .success { background: #d4edda; padding: 15px; border-radius: 4px; margin: 10px 0; color: #155724; }
        .error { background: #f8d7da; padding: 15px; border-radius: 4px; margin: 10px 0; color: #721c24; }
        .info { background: #d1ecf1; padding: 15px; border-radius: 4px; margin: 10px 0; color: #0c5460; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        #results { margin-top: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border-left: 4px solid #007bff; background: #f8f9fa; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 4px; width: 200px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Live API Test - BadBoyz IPTV</h1>
        <p>Testing APIs on <strong>https://badboyzmedia.org</strong></p>
        
        <div class="info">
            <h3>Current Status:</h3>
            <p id="tokenStatus">No token stored</p>
            <p id="sessionStatus">Checking session...</p>
        </div>

        <div class="test-section">
            <h3>Step 1: Login Test</h3>
            <div>
                <input type="text" id="username" placeholder="Username" value="Badboyztv">
                <input type="password" id="password" placeholder="Password" value="Skyblue14!">
                <button class="btn-primary" onclick="testLogin()">🔑 Test Login</button>
            </div>
        </div>

        <div class="test-section">
            <h3>Step 2: Authentication Tests</h3>
            <button class="btn-success" onclick="testAuthVerify()">🔐 Test Auth Verify</button>
            <button class="btn-success" onclick="testDashboard()">📊 Test Dashboard</button>
            <button class="btn-success" onclick="testDebugAPI()">🔧 Test Debug API</button>
        </div>

        <div class="test-section">
            <h3>Step 3: Quick Actions</h3>
            <button class="btn-primary" onclick="openResellerPanel()">🚀 Open Reseller Panel</button>
            <button class="btn-danger" onclick="clearResults()">🗑️ Clear Results</button>
            <button class="btn-danger" onclick="clearToken()">🔓 Clear Token</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        let authToken = localStorage.getItem('authToken');

        function updateStatus() {
            const tokenStatus = document.getElementById('tokenStatus');
            const sessionStatus = document.getElementById('sessionStatus');
            
            if (authToken) {
                tokenStatus.innerHTML = `✅ Token stored: ${authToken.substring(0, 20)}...`;
                tokenStatus.className = 'success';
            } else {
                tokenStatus.innerHTML = '❌ No token stored';
                tokenStatus.className = 'error';
            }
        }

        function addResult(title, content, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `<h4>${title}</h4><pre>${JSON.stringify(content, null, 2)}</pre>`;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function clearToken() {
            localStorage.removeItem('authToken');
            authToken = null;
            updateStatus();
            addResult('🔓 Token Cleared', { message: 'Authentication token removed from localStorage' }, 'info');
        }

        function openResellerPanel() {
            window.open('https://badboyzmedia.org/reseller-panel.html', '_blank');
        }

        async function testLogin() {
            console.log('=== Login Test ===');
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch('https://badboyzmedia.org/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                console.log('Login response:', data);
                
                if (response.ok && data.success && data.data.token) {
                    authToken = data.data.token;
                    localStorage.setItem('authToken', authToken);
                    updateStatus();
                    
                    addResult('🔑 Login Test - SUCCESS', {
                        status: response.status,
                        token: authToken.substring(0, 30) + '...',
                        user: data.data.user,
                        expires_in: data.data.expires_in
                    }, 'success');
                } else {
                    addResult('🔑 Login Test - FAILED', {
                        status: response.status,
                        statusText: response.statusText,
                        data: data
                    }, 'error');
                }
            } catch (error) {
                console.error('Login error:', error);
                addResult('🔑 Login Test - ERROR', { error: error.message }, 'error');
            }
        }

        async function testAuthVerify() {
            console.log('=== Auth Verify Test ===');
            
            if (!authToken) {
                addResult('🔐 Auth Verify Test', { error: 'No token available. Please login first.' }, 'error');
                return;
            }

            try {
                const response = await fetch('https://badboyzmedia.org/api/auth/verify', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const data = await response.json();
                console.log('Auth verify response:', data);
                
                addResult('🔐 Auth Verify Test', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, response.ok ? 'success' : 'error');
            } catch (error) {
                console.error('Auth verify error:', error);
                addResult('🔐 Auth Verify Test', { error: error.message }, 'error');
            }
        }

        async function testDashboard() {
            console.log('=== Dashboard Test ===');
            
            if (!authToken) {
                addResult('📊 Dashboard Test', { error: 'No token available. Please login first.' }, 'error');
                return;
            }

            try {
                const response = await fetch('https://badboyzmedia.org/api/dashboard', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`
                    }
                });
                
                const data = await response.json();
                console.log('Dashboard response:', data);
                
                addResult('📊 Dashboard Test', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, response.ok ? 'success' : 'error');
            } catch (error) {
                console.error('Dashboard error:', error);
                addResult('📊 Dashboard Test', { error: error.message }, 'error');
            }
        }

        async function testDebugAPI() {
            console.log('=== Debug API Test ===');
            
            try {
                const response = await fetch('https://badboyzmedia.org/api/debug', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': authToken ? `Bearer ${authToken}` : ''
                    }
                });
                
                const data = await response.json();
                console.log('Debug response:', data);
                
                addResult('🔧 Debug API Test', {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                }, response.ok ? 'success' : 'error');
            } catch (error) {
                console.error('Debug error:', error);
                addResult('🔧 Debug API Test', { error: error.message }, 'error');
            }
        }

        // Initialize on page load
        window.addEventListener('load', function() {
            updateStatus();
            console.log('Live API Test loaded');
            console.log('Current token:', authToken ? authToken.substring(0, 20) + '...' : 'None');
        });
    </script>
</body>
</html>
