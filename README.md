# BadBoyz IPTV Website

A professional, responsive IPTV service website built with modern web technologies.

## Features

### Core Functionality
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices
- **Three Pricing Tiers**: $25, $30, and $35 plans with detailed feature comparisons
- **Live Chat System**: Integrated chat with automatic Telegram fallback
- **Modern UI/UX**: Clean, professional design with smooth animations
- **Cross-Browser Compatible**: Works on Chrome, Firefox, Safari, and Edge

### Technical Features
- **Fast Loading**: Optimized CSS and JavaScript for quick page loads
- **Accessibility**: WCAG compliant with keyboard navigation support
- **SEO Optimized**: Semantic HTML structure with proper meta tags
- **Progressive Enhancement**: Works without JavaScript, enhanced with it
- **Mobile-First Design**: Responsive breakpoints for all screen sizes

## Pricing Plans

### Basic Plan - $25/month
- 5,000+ Live Channels
- HD Quality Streaming
- 2 Device Connections
- Basic EPG Guide
- Email Support

### Standard Plan - $30/month (Most Popular)
- 8,000+ Live Channels
- Full HD & 4K Quality
- 4 Device Connections
- Advanced EPG Guide
- VOD Library (10,000+ titles)
- Priority Support

### Premium Plan - $35/month
- 12,000+ Live Channels
- Ultra HD & 4K Quality
- 6 Device Connections
- Premium EPG Guide
- VOD Library (25,000+ titles)
- 24/7 Live Chat Support
- Catch-up TV (7 days)

## File Structure

```
badboyz-iptv/
├── index.html          # Main HTML file
├── styles.css          # CSS styles and responsive design
├── script.js           # JavaScript functionality
└── README.md           # This file
```

## Setup Instructions

### Option 1: Simple File Server (Recommended for testing)
1. Open terminal/command prompt in the project directory
2. Run one of these commands:
   - **Python 3**: `python -m http.server 8000`
   - **Python 2**: `python -m SimpleHTTPServer 8000`
   - **Node.js**: `npx http-server -p 8000`
   - **PHP**: `php -S localhost:8000`
3. Open browser and navigate to `http://localhost:8000`

### Option 2: Direct File Opening
1. Double-click `index.html` to open in your default browser
2. Note: Some features may not work due to CORS restrictions

### Option 3: Web Server Deployment
1. Upload all files to your web server
2. Ensure the server supports HTML, CSS, and JavaScript
3. No server-side processing required

## Browser Compatibility

- **Chrome**: 60+
- **Firefox**: 55+
- **Safari**: 12+
- **Edge**: 79+
- **Mobile browsers**: iOS Safari 12+, Chrome Mobile 60+

## Dependencies

### External Libraries (CDN)
- **Font Awesome 6.4.0**: Icons and symbols
- **Google Fonts (Inter)**: Typography
- **No jQuery or heavy frameworks**: Pure vanilla JavaScript

### No Build Process Required
- No compilation or bundling needed
- Ready to deploy as-is
- All dependencies loaded via CDN

## Features in Detail

### Live Chat System
- **Intelligent Bot Responses**: Keyword-based automatic responses
- **Timeout Handling**: 30-second timeout before Telegram redirect
- **Telegram Integration**: Seamless fallback to Telegram group
- **Support Hours**: Different behavior during/outside business hours
- **Mobile Optimized**: Touch-friendly chat interface

### Responsive Design Breakpoints
- **Desktop**: 1200px and above
- **Tablet**: 768px - 1199px
- **Mobile**: Below 768px
- **Small Mobile**: Below 480px

### Performance Optimizations
- **CSS**: Minified and optimized selectors
- **JavaScript**: Debounced scroll events and lazy loading
- **Images**: Lazy loading support (when images are added)
- **Animations**: Reduced motion support for accessibility

## Customization

### Colors (CSS Variables)
```css
:root {
    --primary-color: #6366f1;      /* Main brand color */
    --secondary-color: #f59e0b;    /* Accent color */
    --accent-color: #10b981;       /* Success/check color */
}
```

### Telegram Group URL
Update the Telegram URL in `script.js`:
```javascript
const TELEGRAM_GROUP_URL = 'https://t.me/+sG2pVTojSmg4MGU5';
```

### Pricing Plans
Modify pricing in both `index.html` and `script.js` for consistency.

### Support Hours
Adjust support availability in `script.js`:
```javascript
const SUPPORT_AVAILABLE_HOURS = { start: 9, end: 21 }; // 9 AM to 9 PM
```

## Testing Checklist

### Functionality Tests
- [ ] Navigation menu works on all devices
- [ ] Live chat opens and responds
- [ ] Telegram redirect works
- [ ] Pricing plan selection works
- [ ] All buttons and links function
- [ ] Mobile menu toggles correctly

### Responsive Tests
- [ ] Desktop (1920x1080, 1366x768)
- [ ] Tablet (768x1024, 1024x768)
- [ ] Mobile (375x667, 414x896, 360x640)
- [ ] Test in both portrait and landscape

### Browser Tests
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)
- [ ] Mobile browsers

### Performance Tests
- [ ] Page loads in under 3 seconds
- [ ] No console errors
- [ ] Smooth animations
- [ ] Chat system responds quickly

## Deployment

### Static Hosting (Recommended)
- **Netlify**: Drag and drop the folder
- **Vercel**: Connect to Git repository
- **GitHub Pages**: Push to repository and enable Pages
- **Firebase Hosting**: Use Firebase CLI

### Traditional Web Hosting
- Upload files via FTP/SFTP
- Ensure proper file permissions
- Test all functionality after upload

## Support and Maintenance

### Regular Updates
- Update Font Awesome CDN link periodically
- Check Google Fonts for new versions
- Monitor browser compatibility

### Analytics Integration
- Add Google Analytics tracking code
- Implement conversion tracking for plan selections
- Monitor chat usage and Telegram redirects

### Security Considerations
- No sensitive data stored client-side
- All external links open in new tabs
- Input sanitization in chat system

## License

This project is created for BadBoyz IPTV. All rights reserved.

## Contact

For technical support or customization requests, please contact through the Telegram group: https://t.me/+sG2pVTojSmg4MGU5
