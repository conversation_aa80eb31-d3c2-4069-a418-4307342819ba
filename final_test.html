<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final System Test - BadBoyz IPTV</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        button { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }
        .step { margin: 10px 0; padding: 10px; border-left: 4px solid #007bff; background: #f8f9fa; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Final System Test</h1>
        <p>Complete test of the BadBoyz IPTV reseller system with fixes applied.</p>
        
        <div class="step">
            <h3>Step 1: Test Authentication Flow</h3>
            <button class="btn-primary" onclick="testFullAuth()">Test Complete Auth Flow</button>
            <div id="authResult"></div>
        </div>
        
        <div class="step">
            <h3>Step 2: Test JavaScript Functions</h3>
            <button class="btn-primary" onclick="testJSFunctions()">Test JS Functions</button>
            <div id="jsResult"></div>
        </div>
        
        <div class="step">
            <h3>Step 3: Test Customer Registration</h3>
            <button class="btn-primary" onclick="testRegistration()">Test Registration</button>
            <div id="regResult"></div>
        </div>
        
        <div class="step">
            <h3>Step 4: Open Reseller Panel</h3>
            <button class="btn-success" onclick="openPanel()">Open Reseller Panel</button>
            <p><small>This will open the working reseller panel</small></p>
        </div>
        
        <div class="step">
            <h3>Step 5: Cleanup</h3>
            <button class="btn-danger" onclick="showCleanup()">Show Cleanup Instructions</button>
            <div id="cleanupResult"></div>
        </div>
    </div>

    <script src="/reseller-script-api.js"></script>
    <script>
        const API_BASE_URL = '/api';
        let authToken = null;
        
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-section ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }
        
        async function apiRequest(endpoint, options = {}) {
            const url = `${API_BASE_URL}${endpoint}`;
            const config = {
                headers: {
                    'Content-Type': 'application/json',
                    ...options.headers
                },
                ...options
            };
            
            if (authToken) {
                config.headers['Authorization'] = `Bearer ${authToken}`;
            }
            
            const response = await fetch(url, config);
            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.error || data.message || `HTTP ${response.status}`);
            }
            
            return data;
        }
        
        async function testFullAuth() {
            const container = document.getElementById('authResult');
            container.innerHTML = '';
            
            try {
                // Step 1: Login
                addResult('authResult', '🔄 Step 1: Testing login...', 'info');
                const loginResponse = await apiRequest('/auth/login', {
                    method: 'POST',
                    body: JSON.stringify({
                        username: 'Badboyztv',
                        password: 'Skyblue14!'
                    })
                });
                
                if (loginResponse.success && loginResponse.data.token) {
                    authToken = loginResponse.data.token;
                    addResult('authResult', '✅ Login successful!', 'success');
                    
                    // Step 2: Immediate verification
                    addResult('authResult', '🔄 Step 2: Testing immediate verification...', 'info');
                    const verifyResponse = await apiRequest('/auth/verify');
                    if (verifyResponse.success) {
                        addResult('authResult', '✅ Token verification successful!', 'success');
                        
                        // Step 3: Dashboard
                        addResult('authResult', '🔄 Step 3: Testing dashboard...', 'info');
                        const dashResponse = await apiRequest('/dashboard');
                        if (dashResponse.success) {
                            addResult('authResult', '✅ Dashboard loaded successfully!', 'success');
                            addResult('authResult', `📊 Found ${dashResponse.data.total_customers || 0} customers`, 'info');
                        } else {
                            addResult('authResult', '❌ Dashboard failed', 'error');
                        }
                    } else {
                        addResult('authResult', '❌ Token verification failed', 'error');
                    }
                } else {
                    addResult('authResult', '❌ Login failed', 'error');
                }
            } catch (error) {
                addResult('authResult', `❌ Auth test error: ${error.message}`, 'error');
            }
        }
        
        async function testJSFunctions() {
            const container = document.getElementById('jsResult');
            container.innerHTML = '';
            
            addResult('jsResult', '🔄 Testing JavaScript functions...', 'info');
            
            const functions = ['showSection', 'showAddCustomerModal', 'closeModal', 'toggleSidebar', 'logout'];
            let allExist = true;
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    addResult('jsResult', `✅ Function '${funcName}' exists`, 'success');
                } else {
                    addResult('jsResult', `❌ Function '${funcName}' missing`, 'error');
                    allExist = false;
                }
            });
            
            if (allExist) {
                addResult('jsResult', '✅ All JavaScript functions working!', 'success');
                
                // Test a function
                try {
                    addResult('jsResult', '🔄 Testing showSection function...', 'info');
                    // This would normally change the UI, but we'll just call it
                    if (typeof window.showSection === 'function') {
                        addResult('jsResult', '✅ showSection function callable', 'success');
                    }
                } catch (error) {
                    addResult('jsResult', `❌ Function test error: ${error.message}`, 'error');
                }
            }
        }
        
        async function testRegistration() {
            const container = document.getElementById('regResult');
            container.innerHTML = '';
            
            if (!authToken) {
                addResult('regResult', '⚠️ Please run auth test first', 'warning');
                return;
            }
            
            try {
                addResult('regResult', '🔄 Testing customer registration...', 'info');
                
                const testCustomer = {
                    username: 'testuser_' + Date.now(),
                    email: `test${Date.now()}@example.com`,
                    full_name: 'Test User Final',
                    phone: '1234567890',
                    password: 'testpass123',
                    plan_name: 'Basic',
                    duration_months: 1,
                    max_devices: 2
                };
                
                const response = await apiRequest('/customers/register', {
                    method: 'POST',
                    body: JSON.stringify(testCustomer)
                });
                
                if (response.success) {
                    addResult('regResult', '✅ Customer registration successful!', 'success');
                    addResult('regResult', `👤 Customer: ${testCustomer.username}`, 'info');
                } else {
                    addResult('regResult', '❌ Registration failed', 'error');
                }
            } catch (error) {
                addResult('regResult', `❌ Registration error: ${error.message}`, 'error');
            }
        }
        
        function openPanel() {
            window.open('/reseller-panel.html', '_blank');
        }
        
        function showCleanup() {
            const container = document.getElementById('cleanupResult');
            container.innerHTML = '';
            
            addResult('cleanupResult', '<h4>🧹 Cleanup Instructions:</h4>', 'warning');
            addResult('cleanupResult', '<p><strong>Delete these test files for security:</strong></p><ul><li>fix_auth.php</li><li>update_database.php</li><li>final_test.html</li><li>create_admin.php (if exists)</li></ul>', 'warning');
            addResult('cleanupResult', '<p><strong>Keep these files:</strong></p><ul><li>reseller-panel.html</li><li>reseller-script-api.js</li><li>All files in api/ directory</li><li>All files in config/ directory</li></ul>', 'info');
            addResult('cleanupResult', '<p><strong>Your reseller panel should now be fully working!</strong></p>', 'success');
        }
        
        // Auto-load message
        setTimeout(() => {
            if (typeof window.showSection === 'function') {
                addResult('jsResult', '✅ JavaScript functions loaded automatically', 'success');
            }
        }, 1000);
    </script>
</body>
</html>
