<?php
/**
 * Database Update - Add auth token columns
 * Visit: https://badboyzmedia.org/update_database.php ONCE
 * DELETE this file after running!
 */

require_once 'config/database.php';

echo "<h2>🔧 Database Update</h2>";
echo "<p>Adding authentication token columns...</p>";

try {
    $db = getDB();
    
    // Check if columns already exist
    $stmt = $db->query("SHOW COLUMNS FROM reseller_admins LIKE 'auth_token'");
    $authTokenExists = $stmt->fetch();
    
    $stmt = $db->query("SHOW COLUMNS FROM reseller_admins LIKE 'token_expires'");
    $tokenExpiresExists = $stmt->fetch();
    
    if (!$authTokenExists) {
        echo "<p>Adding auth_token column...</p>";
        $db->exec("ALTER TABLE reseller_admins ADD COLUMN auth_token VARCHAR(255) NULL");
        echo "<p>✅ auth_token column added</p>";
    } else {
        echo "<p>✅ auth_token column already exists</p>";
    }
    
    if (!$tokenExpiresExists) {
        echo "<p>Adding token_expires column...</p>";
        $db->exec("ALTER TABLE reseller_admins ADD COLUMN token_expires DATETIME NULL");
        echo "<p>✅ token_expires column added</p>";
    } else {
        echo "<p>✅ token_expires column already exists</p>";
    }
    
    // Add index for faster token lookups
    try {
        $db->exec("CREATE INDEX idx_auth_token ON reseller_admins(auth_token)");
        echo "<p>✅ Index on auth_token created</p>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
            echo "<p>✅ Index on auth_token already exists</p>";
        } else {
            echo "<p>⚠️ Index creation warning: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<div style='background:#d4edda;padding:15px;border-radius:4px;margin:20px 0;'>";
    echo "<h3>✅ Database Update Complete!</h3>";
    echo "<p>The authentication system has been updated to use database tokens as fallback.</p>";
    echo "<p>This should fix the session authentication issues.</p>";
    echo "</div>";
    
    echo "<h3>Next Steps:</h3>";
    echo "<ol>";
    echo "<li>Delete this file for security</li>";
    echo "<li>Test the reseller panel login</li>";
    echo "<li>Check if dashboard and registration work</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<div style='background:#f8d7da;padding:15px;border-radius:4px;margin:20px 0;'>";
    echo "<h3>❌ Database Update Failed</h3>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "</div>";
}

echo "<p>🔒 <strong>DELETE this file after running!</strong></p>";

echo "<style>body{font-family:Arial,sans-serif;margin:20px;} h2{color:#333;} h3{color:#666;border-bottom:1px solid #eee;} ol{margin:10px 0;} li{margin:5px 0;}</style>";
?>
