<?php
/**
 * Simple API Test - Bypass ModSecurity
 * Visit: https://badboyzmedia.org/api/test.php
 * DELETE this file after testing!
 */

// Start session
session_start();

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Simple test response
$response = [
    'success' => true,
    'message' => 'API is working!',
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $_SERVER['REQUEST_METHOD'],
    'session_id' => session_id(),
    'session_status' => session_status(),
    'php_version' => PHP_VERSION,
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'
];

// Test database connection
try {
    require_once '../config/database.php';
    $db = getDB();
    $stmt = $db->query("SELECT COUNT(*) as count FROM reseller_admins");
    $result = $stmt->fetch();
    $response['database'] = [
        'status' => 'connected',
        'admin_count' => $result['count']
    ];
} catch (Exception $e) {
    $response['database'] = [
        'status' => 'error',
        'error' => $e->getMessage()
    ];
}

// Test authentication if token provided
$headers = getallheaders();
if (isset($headers['Authorization'])) {
    $authHeader = $headers['Authorization'];
    if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
        $token = $matches[1];
        $response['auth_test'] = [
            'token_provided' => true,
            'token_length' => strlen($token),
            'token_preview' => substr($token, 0, 10) . '...'
        ];
        
        // Test session data
        if (isset($_SESSION['admin_id'])) {
            $response['auth_test']['session_data'] = [
                'admin_id' => $_SESSION['admin_id'],
                'username' => $_SESSION['username'] ?? 'unknown',
                'has_token' => isset($_SESSION['token'])
            ];
        } else {
            $response['auth_test']['session_data'] = 'No session data';
        }
    }
} else {
    $response['auth_test'] = [
        'token_provided' => false,
        'message' => 'No Authorization header'
    ];
}

echo json_encode($response, JSON_PRETTY_PRINT);
?>
