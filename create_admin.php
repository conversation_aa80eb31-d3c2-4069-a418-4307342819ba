<?php
/**
 * Create Admin User Script
 * Run this ONCE after importing the database to create the admin user
 * Delete this file after running it!
 */

require_once 'config/database.php';

try {
    $db = getDB();

    // Check if admin already exists and has proper password
    $stmt = $db->prepare("SELECT id, password_hash FROM reseller_admins WHERE username = ?");
    $stmt->execute(['Badboyztv']);
    $existing = $stmt->fetch();

    // Always update/create the admin user to ensure proper setup
    $passwordHash = hashPassword('Skyblue14!');

    if ($existing) {
        // Update existing admin with proper password
        $stmt = $db->prepare("UPDATE reseller_admins SET password_hash = ?, auth_token = NULL, token_expires = NULL WHERE username = ?");
        $result = $stmt->execute([$passwordHash, 'Badboyztv']);
        $action = "updated";
    } else {
        // Create new admin user
        $stmt = $db->prepare("
            INSERT INTO reseller_admins (username, password_hash, email, full_name, role, status)
            VALUES (?, ?, ?, ?, ?, ?)
        ");

        $result = $stmt->execute([
            'Badboyztv',
            $passwordHash,
            '<EMAIL>',
            'BadBoyz Admin',
            'admin',
            'active'
        ]);
        $action = "created";
    }

    if ($result) {
        echo "✅ Admin user $action successfully!<br>";
        echo "<strong>Login Credentials:</strong><br>";
        echo "Username: <strong>Badboyztv</strong><br>";
        echo "Password: <strong>Skyblue14!</strong><br><br>";

        // Test the password hash
        $testStmt = $db->prepare("SELECT password_hash FROM reseller_admins WHERE username = ?");
        $testStmt->execute(['Badboyztv']);
        $testAdmin = $testStmt->fetch();

        if ($testAdmin && verifyPassword('Skyblue14!', $testAdmin['password_hash'])) {
            echo "✅ Password verification test: <strong>PASSED</strong><br>";
        } else {
            echo "❌ Password verification test: <strong>FAILED</strong><br>";
        }

        echo "🔒 <strong>IMPORTANT:</strong> Delete this file (create_admin.php) now for security!<br>";
    } else {
        echo "❌ Failed to $action admin user<br>";
    }
    
    // Test database connection and show table status
    echo "<hr>";
    echo "<h3>Database Status:</h3>";
    
    $tables = ['reseller_admins', 'customers', 'subscription_plans', 'customer_subscriptions', 'payments', 'device_sessions'];
    
    foreach ($tables as $table) {
        try {
            $stmt = $db->query("SELECT COUNT(*) as count FROM `$table`");
            $count = $stmt->fetch()['count'];
            echo "✅ Table '$table': $count records<br>";
        } catch (Exception $e) {
            echo "❌ Table '$table': Error - " . $e->getMessage() . "<br>";
        }
    }
    
    echo "<hr>";
    echo "<h3>Next Steps:</h3>";
    echo "1. ✅ Database is set up<br>";
    echo "2. ✅ Admin user is created<br>";
    echo "3. 🔒 <strong>DELETE THIS FILE (create_admin.php)</strong><br>";
    echo "4. 🚀 Go to <a href='reseller-panel.html'>reseller-panel.html</a> and login<br>";
    echo "5. 📝 Update reseller-panel.html to use reseller-script-api.js<br>";
    
} catch (Exception $e) {
    echo "❌ Database Error: " . $e->getMessage() . "<br>";
    echo "<br><strong>Check your database configuration in config/database.php</strong><br>";
    echo "Make sure you have updated the database credentials correctly.";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h3 { color: #333; }
hr { margin: 20px 0; }
strong { color: #d63384; }
a { color: #0d6efd; }
</style>
