<?php
/**
 * Test Setup Script - Check if everything is working
 * Visit this file to test your setup: https://badboyzmedia.org/test_setup.php
 * DELETE this file after testing!
 */

echo "<h2>🔧 BadBoyz IPTV Setup Test</h2>";

// Test 1: Database Connection
echo "<h3>1. Database Connection Test</h3>";
try {
    if (file_exists('config/database.php')) {
        require_once 'config/database.php';
        $db = getDB();
        echo "✅ Database connection successful!<br>";
        
        // Check if admin user exists
        $stmt = $db->prepare("SELECT username, password_hash FROM reseller_admins WHERE username = ?");
        $stmt->execute(['Badboyztv']);
        $admin = $stmt->fetch();
        
        if ($admin) {
            echo "✅ Admin user 'Badboyztv' found<br>";
            if ($admin['password_hash'] === 'TEMP_HASH_TO_BE_UPDATED') {
                echo "⚠️ Admin password needs to be updated - run create_admin.php<br>";
            } else {
                echo "✅ Admin password is properly set<br>";
            }
        } else {
            echo "❌ Admin user not found - run create_admin.php<br>";
        }
        
    } else {
        echo "❌ config/database.php not found<br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Test 2: API Files
echo "<h3>2. API Files Test</h3>";
$apiFiles = ['api/index.php', 'api/auth.php', 'api/customers.php', 'api/dashboard.php'];
foreach ($apiFiles as $file) {
    if (file_exists($file)) {
        echo "✅ $file exists<br>";
    } else {
        echo "❌ $file missing<br>";
    }
}

// Test 3: JavaScript Files
echo "<h3>3. JavaScript Files Test</h3>";
if (file_exists('reseller-script-api.js')) {
    echo "✅ reseller-script-api.js exists<br>";
} else {
    echo "❌ reseller-script-api.js missing<br>";
}

// Test 4: .htaccess
echo "<h3>4. .htaccess Test</h3>";
if (file_exists('.htaccess')) {
    echo "✅ .htaccess exists<br>";
    $htaccess = file_get_contents('.htaccess');
    if (strpos($htaccess, 'RewriteRule ^api/(.*)$ api/index.php') !== false) {
        echo "✅ API rewrite rule found<br>";
    } else {
        echo "⚠️ API rewrite rule may be missing<br>";
    }
} else {
    echo "❌ .htaccess missing<br>";
}

// Test 5: Direct API Test
echo "<h3>5. Direct API Test</h3>";
if (file_exists('api/index.php')) {
    echo "Testing direct API access...<br>";
    
    // Simulate API request
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_SERVER['REQUEST_URI'] = '/api/auth/login';
    
    ob_start();
    try {
        include 'api/index.php';
        $output = ob_get_contents();
        if (strpos($output, 'json') !== false || strpos($output, '{') !== false) {
            echo "✅ API returns JSON response<br>";
        } else {
            echo "⚠️ API may not be returning JSON<br>";
            echo "Output preview: " . substr($output, 0, 100) . "...<br>";
        }
    } catch (Exception $e) {
        echo "❌ API error: " . $e->getMessage() . "<br>";
    }
    ob_end_clean();
}

// Test 6: Password Hash Test
echo "<h3>6. Password Hash Test</h3>";
if (function_exists('hashPassword')) {
    $testHash = hashPassword('Skyblue14!');
    echo "✅ Password hashing function works<br>";
    echo "Sample hash: " . substr($testHash, 0, 20) . "...<br>";
} else {
    echo "❌ Password hashing function not available<br>";
}

echo "<hr>";
echo "<h3>📋 Summary & Next Steps:</h3>";
echo "<p>If you see mostly ✅ green checkmarks above, your setup is working!</p>";
echo "<p><strong>Next steps:</strong></p>";
echo "<ol>";
echo "<li>If admin password needs updating, run <a href='create_admin.php'>create_admin.php</a></li>";
echo "<li>Test login at <a href='reseller-panel.html'>reseller-panel.html</a></li>";
echo "<li>🔒 <strong>DELETE this file (test_setup.php) for security!</strong></li>";
echo "</ol>";

echo "<style>body{font-family:Arial,sans-serif;margin:20px;} h2{color:#333;} h3{color:#666;border-bottom:1px solid #eee;} .error{color:red;} .success{color:green;} .warning{color:orange;}</style>";
?>
