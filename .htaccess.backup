# Minimal .htaccess for testing - SAFE VERSION

# Enable mod_rewrite
RewriteEngine On

# API URL Rewriting
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^api/(.*)$ api/index.php [QSA,L]

# Basic security - deny access to sensitive files
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|sql|conf|bak)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Prevent directory browsing
Options -Indexes
