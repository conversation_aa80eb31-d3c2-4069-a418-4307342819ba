<?php
/**
 * Database Configuration for BadBoyz IPTV
 * For InMotion Hosting / cPanel
 */

// Database Configuration
// Update these values with your cPanel database details
define('DB_HOST', 'localhost'); // Usually 'localhost' for cPanel
define('DB_NAME', 'c0e0425_badboyz_iptv'); // Format: cpanel_username_database_name
define('DB_USER', 'c0e0425_badboyz_user'); // Format: cpanel_username_database_user
define('DB_PASS', 'wyg]Fhehyol1'); // Your database password
define('DB_CHARSET', 'utf8mb4');

// Application Configuration
define('APP_NAME', 'BadBoyz IPTV Reseller Panel');
define('APP_VERSION', '1.0.0');
define('APP_DEBUG', false); // Set to false in production

// Security Configuration
define('JWT_SECRET', 'your-super-secret-jwt-key-change-this-in-production');
define('PASSWORD_SALT', 'badboyz-iptv-salt-2024');
define('SESSION_TIMEOUT', 3600); // 1 hour in seconds

// API Configuration
define('API_VERSION', 'v1');
define('API_BASE_URL', '/api/v1/');

// File Upload Configuration
define('UPLOAD_MAX_SIZE', 5 * 1024 * 1024); // 5MB
define('UPLOAD_ALLOWED_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'pdf']);

class Database {
    private static $instance = null;
    private $connection;
    
    private function __construct() {
        try {
            $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . DB_CHARSET
            ];
            
            $this->connection = new PDO($dsn, DB_USER, DB_PASS, $options);
            
        } catch (PDOException $e) {
            if (APP_DEBUG) {
                die("Database connection failed: " . $e->getMessage());
            } else {
                die("Database connection failed. Please contact support.");
            }
        }
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        return $this->connection;
    }
    
    // Prevent cloning
    private function __clone() {}
    
    // Prevent unserialization
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

// Helper function to get database connection
function getDB() {
    return Database::getInstance()->getConnection();
}

// Helper function for secure password hashing
function hashPassword($password) {
    return password_hash($password . PASSWORD_SALT, PASSWORD_DEFAULT);
}

// Helper function for password verification
function verifyPassword($password, $hash) {
    return password_verify($password . PASSWORD_SALT, $hash);
}

// Helper function for generating secure tokens
function generateToken($length = 32) {
    return bin2hex(random_bytes($length));
}

// Helper function for sanitizing input
function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

// Helper function for JSON response
function jsonResponse($data, $status = 200) {
    http_response_code($status);
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    
    if ($status >= 400) {
        echo json_encode(['error' => $data]);
    } else {
        echo json_encode(['success' => true, 'data' => $data]);
    }
    exit;
}

// Helper function for error response
function errorResponse($message, $status = 400) {
    jsonResponse($message, $status);
}

// Helper function for success response
function successResponse($data) {
    jsonResponse($data, 200);
}

// Helper function to validate email
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Helper function to validate phone
function isValidPhone($phone) {
    return preg_match('/^[\+]?[1-9][\d]{0,15}$/', $phone);
}

// Helper function to log errors
function logError($message, $file = 'error.log') {
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message" . PHP_EOL;
    error_log($logMessage, 3, __DIR__ . "/../logs/$file");
}

// Initialize error reporting based on debug mode
if (APP_DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Set timezone
date_default_timezone_set('America/New_York');

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
?>
