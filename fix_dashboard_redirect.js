/**
 * Dashboard Redirect Fix
 * This script fixes the dashboard redirection issue
 * Include this after the main reseller-script-api.js
 */

console.log('🔧 Dashboard redirect fix loaded');

// Override the original functions with improved versions
(function() {
    'use strict';
    
    // Store original functions
    const originalShowDashboard = window.showDashboard;
    const originalHandleLogin = window.handleLogin;
    
    // Enhanced showDashboard function
    window.showDashboard = function() {
        console.log('🔧 Enhanced showDashboard() called');
        
        const loginScreen = document.getElementById('loginScreen');
        const dashboard = document.getElementById('dashboard');
        
        console.log('Login screen element:', loginScreen);
        console.log('Dashboard element:', dashboard);
        
        if (loginScreen) {
            loginScreen.classList.add('hidden');
            loginScreen.style.display = 'none';
            console.log('✅ Login screen hidden');
        } else {
            console.error('❌ Login screen element not found');
        }
        
        if (dashboard) {
            dashboard.classList.remove('hidden');
            dashboard.style.display = 'flex';
            console.log('✅ Dashboard shown');
            
            // Force visibility with CSS
            dashboard.style.visibility = 'visible';
            dashboard.style.opacity = '1';
            
            // Trigger any layout recalculation
            dashboard.offsetHeight;
            
        } else {
            console.error('❌ Dashboard element not found');
        }
        
        // Update body class if needed
        document.body.classList.remove('login-active');
        document.body.classList.add('dashboard-active');
        
        console.log('🔧 Dashboard display completed');
    };
    
    // Enhanced login success handler
    function handleLoginSuccess(response) {
        console.log('🔧 Enhanced login success handler');
        console.log('Response:', response);
        
        // Store auth data
        const token = response.data.token;
        const user = response.data.user;
        
        localStorage.setItem('authToken', token);
        localStorage.setItem('userInfo', JSON.stringify(user));
        
        console.log('✅ Auth data stored');
        console.log('Token:', token.substring(0, 20) + '...');
        
        // Force dashboard display with multiple attempts
        let attempts = 0;
        const maxAttempts = 5;
        
        function tryShowDashboard() {
            attempts++;
            console.log(`🔧 Dashboard display attempt ${attempts}/${maxAttempts}`);
            
            window.showDashboard();
            
            // Check if dashboard is actually visible
            const dashboard = document.getElementById('dashboard');
            const isVisible = dashboard && !dashboard.classList.contains('hidden') && 
                             getComputedStyle(dashboard).display !== 'none';
            
            if (isVisible) {
                console.log('✅ Dashboard successfully displayed');
                
                // Load dashboard data
                if (typeof window.loadDashboardData === 'function') {
                    window.loadDashboardData();
                }
                
                // Show success notification
                if (typeof window.showNotification === 'function') {
                    window.showNotification(`Welcome back, ${user.full_name}!`, 'success');
                }
                
            } else if (attempts < maxAttempts) {
                console.log(`⚠️ Dashboard not visible, retrying in 200ms...`);
                setTimeout(tryShowDashboard, 200);
            } else {
                console.error('❌ Failed to display dashboard after all attempts');
                
                // Fallback: redirect to dashboard URL if available
                if (window.location.hash !== '#dashboard') {
                    console.log('🔧 Trying hash-based navigation fallback');
                    window.location.hash = '#dashboard';
                }
            }
        }
        
        // Start the dashboard display process
        setTimeout(tryShowDashboard, 100);
    }
    
    // Monitor for successful logins
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        const [url, options] = args;
        
        // Check if this is a login request
        if (url.includes('/api/auth/login') && options && options.method === 'POST') {
            console.log('🔧 Intercepting login request');
            
            return originalFetch.apply(this, args)
                .then(response => {
                    // Clone response to read it without consuming it
                    const responseClone = response.clone();
                    
                    if (response.ok) {
                        responseClone.json().then(data => {
                            if (data.success) {
                                console.log('🔧 Login successful, triggering enhanced handler');
                                setTimeout(() => handleLoginSuccess(data), 50);
                            }
                        }).catch(err => {
                            console.error('🔧 Error parsing login response:', err);
                        });
                    }
                    
                    return response;
                })
                .catch(error => {
                    console.error('🔧 Login request failed:', error);
                    throw error;
                });
        }
        
        return originalFetch.apply(this, args);
    };
    
    // Auto-check for existing token on page load
    function checkExistingAuth() {
        const token = localStorage.getItem('authToken');
        if (token) {
            console.log('🔧 Found existing token, verifying...');
            
            fetch('/api/auth/verify', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('🔧 Existing token valid, showing dashboard');
                    setTimeout(() => window.showDashboard(), 100);
                } else {
                    console.log('🔧 Existing token invalid, clearing');
                    localStorage.removeItem('authToken');
                    localStorage.removeItem('userInfo');
                }
            })
            .catch(error => {
                console.error('🔧 Token verification failed:', error);
                localStorage.removeItem('authToken');
                localStorage.removeItem('userInfo');
            });
        }
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', checkExistingAuth);
    } else {
        checkExistingAuth();
    }
    
    console.log('🔧 Dashboard redirect fix initialized');
    
})();
