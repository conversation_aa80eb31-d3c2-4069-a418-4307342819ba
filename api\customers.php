<?php
/**
 * Customers API Endpoints
 */

require_once 'auth.php';

function handleCustomersRequest($method, $pathParts, $input) {
    switch ($method) {
        case 'GET':
            if (isset($pathParts[1]) && is_numeric($pathParts[1])) {
                getCustomer($pathParts[1]);
            } else {
                getCustomers();
            }
            break;
            
        case 'POST':
            if (isset($pathParts[1]) && $pathParts[1] === 'register') {
                registerCustomer($input);
            } else {
                createCustomer($input);
            }
            break;
            
        case 'PUT':
            if (isset($pathParts[1]) && is_numeric($pathParts[1])) {
                updateCustomer($pathParts[1], $input);
            } else {
                errorResponse('Customer ID required', 400);
            }
            break;
            
        case 'DELETE':
            if (isset($pathParts[1]) && is_numeric($pathParts[1])) {
                deleteCustomer($pathParts[1]);
            } else {
                errorResponse('Customer ID required', 400);
            }
            break;
            
        default:
            errorResponse('Method not allowed', 405);
    }
}

function getCustomers() {
    $adminId = requireAuth();
    
    $page = (int)($_GET['page'] ?? 1);
    $limit = min((int)($_GET['limit'] ?? 20), 100);
    $search = $_GET['search'] ?? '';
    $status = $_GET['status'] ?? '';
    
    $db = getDB();
    
    // Build query
    $where = ['1=1'];
    $params = [];
    
    if ($search) {
        $where[] = "(c.username LIKE ? OR c.email LIKE ? OR c.full_name LIKE ?)";
        $searchTerm = "%$search%";
        $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm]);
    }
    
    if ($status) {
        $where[] = "c.status = ?";
        $params[] = $status;
    }
    
    $whereClause = implode(' AND ', $where);
    
    $query = "
        SELECT c.*, ra.username as created_by_username
        FROM customers c
        LEFT JOIN reseller_admins ra ON c.created_by = ra.id
        WHERE $whereClause
        ORDER BY c.created_at DESC
    ";
    
    $result = paginate($query, $params, $page, $limit);
    
    // Format customer data
    $customers = array_map('formatCustomer', $result['data']);
    
    successResponse([
        'customers' => $customers,
        'pagination' => $result['pagination']
    ]);
}

function getCustomer($customerId) {
    $adminId = requireAuth();
    
    $db = getDB();
    $stmt = $db->prepare("
        SELECT c.*, ra.username as created_by_username
        FROM customers c
        LEFT JOIN reseller_admins ra ON c.created_by = ra.id
        WHERE c.id = ?
    ");
    $stmt->execute([$customerId]);
    $customer = $stmt->fetch();
    
    if (!$customer) {
        errorResponse('Customer not found', 404);
    }
    
    $formattedCustomer = formatCustomer($customer);
    
    // Get subscription history
    $stmt = $db->prepare("
        SELECT cs.*, sp.name as plan_name, sp.price
        FROM customer_subscriptions cs
        JOIN subscription_plans sp ON cs.plan_id = sp.id
        WHERE cs.customer_id = ?
        ORDER BY cs.created_at DESC
    ");
    $stmt->execute([$customerId]);
    $subscriptions = $stmt->fetchAll();
    
    // Get payment history
    $stmt = $db->prepare("
        SELECT p.*, ra.username as processed_by_username
        FROM payments p
        LEFT JOIN reseller_admins ra ON p.processed_by = ra.id
        WHERE p.customer_id = ?
        ORDER BY p.payment_date DESC
    ");
    $stmt->execute([$customerId]);
    $payments = $stmt->fetchAll();
    
    $formattedCustomer['subscription_history'] = $subscriptions;
    $formattedCustomer['payment_history'] = $payments;
    
    successResponse($formattedCustomer);
}

function registerCustomer($input) {
    $adminId = requireAuth();
    
    validateRequired($input, [
        'username', 'email', 'full_name', 'password',
        'plan_name', 'duration_months'
    ]);
    
    $db = getDB();
    
    try {
        $db->beginTransaction();
        
        // Check if username or email exists
        $stmt = $db->prepare("
            SELECT id FROM customers 
            WHERE username = ? OR email = ?
        ");
        $stmt->execute([$input['username'], $input['email']]);
        if ($stmt->fetch()) {
            errorResponse('Username or email already exists', 409);
        }
        
        // Get plan details by name
        $stmt = $db->prepare("
            SELECT * FROM subscription_plans
            WHERE name = ? AND status = 'active'
        ");
        $stmt->execute([$input['plan_name']]);
        $plan = $stmt->fetch();
        
        if (!$plan) {
            errorResponse('Invalid subscription plan', 400);
        }
        
        // Create customer
        $passwordHash = hashPassword($input['password']);
        
        $stmt = $db->prepare("
            INSERT INTO customers 
            (username, email, password_hash, full_name, phone, max_devices, created_by, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, 'active')
        ");
        $stmt->execute([
            sanitizeInput($input['username']),
            sanitizeInput($input['email']),
            $passwordHash,
            sanitizeInput($input['full_name']),
            sanitizeInput($input['phone'] ?? ''),
            (int)($input['max_devices'] ?? $plan['max_devices']),
            $adminId
        ]);
        
        $customerId = $db->lastInsertId();
        
        // Create subscription
        $startDate = date('Y-m-d');
        $endDate = date('Y-m-d', strtotime("+{$input['duration_months']} months"));
        
        $stmt = $db->prepare("
            INSERT INTO customer_subscriptions 
            (customer_id, plan_id, start_date, end_date, created_by, status)
            VALUES (?, ?, ?, ?, ?, 'active')
        ");
        $stmt->execute([$customerId, $input['plan_id'], $startDate, $endDate, $adminId]);
        
        $subscriptionId = $db->lastInsertId();
        
        // Create payment record
        $totalAmount = $plan['price'] * $input['duration_months'];
        
        $stmt = $db->prepare("
            INSERT INTO payments 
            (customer_id, subscription_id, amount, status, processed_by)
            VALUES (?, ?, ?, 'completed', ?)
        ");
        $stmt->execute([$customerId, $subscriptionId, $totalAmount, $adminId]);
        
        $db->commit();
        
        // Get the created customer
        $stmt = $db->prepare("SELECT * FROM customers WHERE id = ?");
        $stmt->execute([$customerId]);
        $customer = $stmt->fetch();
        
        logError("Customer registered: {$input['username']} by admin ID: $adminId");
        
        successResponse([
            'customer' => formatCustomer($customer),
            'message' => 'Customer registered successfully'
        ]);
        
    } catch (Exception $e) {
        $db->rollBack();
        logError("Customer registration error: " . $e->getMessage());
        errorResponse('Registration failed', 500);
    }
}

function updateCustomer($customerId, $input) {
    $adminId = requireAuth();
    
    $db = getDB();
    
    // Check if customer exists
    $stmt = $db->prepare("SELECT * FROM customers WHERE id = ?");
    $stmt->execute([$customerId]);
    $customer = $stmt->fetch();
    
    if (!$customer) {
        errorResponse('Customer not found', 404);
    }
    
    // Build update query
    $updates = [];
    $params = [];
    
    $allowedFields = ['full_name', 'email', 'phone', 'max_devices', 'status', 'notes'];
    
    foreach ($allowedFields as $field) {
        if (isset($input[$field])) {
            $updates[] = "$field = ?";
            $params[] = sanitizeInput($input[$field]);
        }
    }
    
    if (empty($updates)) {
        errorResponse('No valid fields to update', 400);
    }
    
    $params[] = $customerId;
    
    try {
        $stmt = $db->prepare("
            UPDATE customers 
            SET " . implode(', ', $updates) . " 
            WHERE id = ?
        ");
        $stmt->execute($params);
        
        // Get updated customer
        $stmt = $db->prepare("SELECT * FROM customers WHERE id = ?");
        $stmt->execute([$customerId]);
        $updatedCustomer = $stmt->fetch();
        
        logError("Customer updated: ID $customerId by admin ID: $adminId");
        
        successResponse([
            'customer' => formatCustomer($updatedCustomer),
            'message' => 'Customer updated successfully'
        ]);
        
    } catch (Exception $e) {
        logError("Customer update error: " . $e->getMessage());
        errorResponse('Update failed', 500);
    }
}

function deleteCustomer($customerId) {
    $adminId = requireAuth();

    $db = getDB();

    // Check if customer exists
    $stmt = $db->prepare("SELECT username FROM customers WHERE id = ?");
    $stmt->execute([$customerId]);
    $customer = $stmt->fetch();

    if (!$customer) {
        errorResponse('Customer not found', 404);
    }

    try {
        // Delete customer (cascade will handle related records)
        $stmt = $db->prepare("DELETE FROM customers WHERE id = ?");
        $stmt->execute([$customerId]);

        logError("Customer deleted: {$customer['username']} (ID: $customerId) by admin ID: $adminId");

        successResponse(['message' => 'Customer deleted successfully']);

    } catch (Exception $e) {
        logError("Customer deletion error: " . $e->getMessage());
        errorResponse('Deletion failed', 500);
    }
}

function extendCustomerSubscription($customerId, $months) {
    $adminId = requireAuth();

    $db = getDB();

    try {
        $db->beginTransaction();

        // Get current active subscription
        $stmt = $db->prepare("
            SELECT * FROM customer_subscriptions
            WHERE customer_id = ? AND status = 'active'
            ORDER BY end_date DESC LIMIT 1
        ");
        $stmt->execute([$customerId]);
        $subscription = $stmt->fetch();

        if ($subscription) {
            // Extend existing subscription
            $newEndDate = date('Y-m-d', strtotime($subscription['end_date'] . " +$months months"));

            $stmt = $db->prepare("
                UPDATE customer_subscriptions
                SET end_date = ?
                WHERE id = ?
            ");
            $stmt->execute([$newEndDate, $subscription['id']]);
        } else {
            errorResponse('No active subscription found', 404);
        }

        // Update customer status to active
        $stmt = $db->prepare("UPDATE customers SET status = 'active' WHERE id = ?");
        $stmt->execute([$customerId]);

        $db->commit();

        updateCustomerStatus($customerId);

        successResponse(['message' => "Subscription extended by $months months"]);

    } catch (Exception $e) {
        $db->rollBack();
        logError("Subscription extension error: " . $e->getMessage());
        errorResponse('Extension failed', 500);
    }
}
?>
