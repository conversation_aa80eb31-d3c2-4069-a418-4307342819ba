// DOM Elements
const navToggle = document.querySelector('.nav-toggle');
const navMenu = document.querySelector('.nav-menu');
const navLinks = document.querySelectorAll('.nav-link');
const chatModal = document.getElementById('chatModal');
const chatBubble = document.getElementById('chatBubble');
const liveChatBtns = document.querySelectorAll('#liveChatBtn, #contactChatBtn');
const chatClose = document.getElementById('chatClose');
const chatMinimize = document.getElementById('chatMinimize');
const chatInput = document.getElementById('chatInput');
const sendMessageBtn = document.getElementById('sendMessage');
const chatMessages = document.getElementById('chatMessages');
const typingIndicator = document.getElementById('typingIndicator');
const quickReplies = document.getElementById('quickReplies');
const chatNotification = document.getElementById('chatNotification');

// Configuration
const TELEGRAM_GROUP_URL = 'https://t.me/+sG2pVTojSmg4MGU5';
const CHAT_TIMEOUT = 30000; // 30 seconds timeout for chat response
const SUPPORT_AVAILABLE_HOURS = { start: 9, end: 21 }; // 9 AM to 9 PM

// State
let chatActive = false;
let chatTimeout = null;
let messageCount = 0;
let isTyping = false;
let conversationContext = [];
let userSession = {
    name: null,
    email: null,
    interestedPlan: null,
    deviceType: null,
    hasTrialRequested: false
};

// Advanced Knowledge Base
const knowledgeBase = {
    pricing: {
        basic: {
            price: '$25',
            channels: '5,000+',
            quality: 'HD',
            devices: '2',
            features: ['5,000+ Live Channels', 'HD Quality Streaming', '2 Device Connections', 'Basic EPG Guide', 'Email Support']
        },
        standard: {
            price: '$30',
            channels: '8,000+',
            quality: 'Full HD & 4K',
            devices: '4',
            features: ['8,000+ Live Channels', 'Full HD & 4K Quality', '4 Device Connections', 'Advanced EPG Guide', 'VOD Library (10,000+ titles)', 'Priority Support']
        },
        premium: {
            price: '$35',
            channels: '12,000+',
            quality: 'Ultra HD & 4K',
            devices: '6',
            features: ['12,000+ Live Channels', 'Ultra HD & 4K Quality', '6 Device Connections', 'Premium EPG Guide', 'VOD Library (25,000+ titles)', '24/7 Live Chat Support', 'Catch-up TV (7 days)']
        }
    },
    devices: {
        smarttv: ['Samsung Smart TV', 'LG Smart TV', 'Sony Android TV', 'TCL Smart TV', 'Hisense Smart TV'],
        mobile: ['iPhone/iPad (iOS 12+)', 'Android phones/tablets', 'Windows Mobile'],
        computer: ['Windows PC', 'Mac', 'Linux', 'Chrome OS'],
        streaming: ['Amazon Fire Stick/TV', 'Roku', 'Apple TV', 'Chromecast', 'NVIDIA Shield'],
        settop: ['MAG Boxes', 'Formuler', 'Dreambox', 'Enigma2', 'IPTV Boxes'],
        gaming: ['Xbox', 'PlayStation', 'Nintendo Switch (browser)']
    },
    installation: {
        firestick: 'Install our app from Amazon App Store or sideload using Downloader app',
        android: 'Download our Android app or use any IPTV player like TiviMate, IPTV Smarters',
        ios: 'Use GSE Smart IPTV or IPTV Smarters Pro from App Store',
        smarttv: 'Install Smart IPTV app or use built-in IPTV player',
        mag: 'Configure with our portal URL and your MAC address',
        pc: 'Use VLC Media Player, IPTV Smarters, or our Windows app'
    },
    features: {
        epg: 'Electronic Program Guide with 7-day TV schedule',
        catchup: 'Watch shows from the past 7 days (Premium plan)',
        vod: 'Video on Demand library with latest movies and TV series',
        multiscreen: 'Watch on multiple devices simultaneously',
        recording: 'Cloud DVR functionality (Premium plan)',
        quality: 'Adaptive streaming from SD to 4K based on your connection'
    }
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigation();
    initializeChatSystem();
    initializeSmoothScrolling();
    initializeAnimations();
    checkSupportAvailability();
    initializeChatBubble();
    showWelcomeMessage();
});

// Navigation functionality
function initializeNavigation() {
    // Mobile menu toggle
    navToggle.addEventListener('click', function() {
        navMenu.classList.toggle('active');
        navToggle.classList.toggle('active');
    });

    // Close mobile menu when clicking on links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = link.getAttribute('href');

            // Always close mobile menu
            navMenu.classList.remove('active');
            navToggle.classList.remove('active');

            // For reseller panel or other external links, allow normal navigation
            if (href && !href.startsWith('#')) {
                // Let the browser handle the navigation normally
                return true;
            }
        });
    });

    // Close mobile menu when clicking outside
    document.addEventListener('click', function(e) {
        if (!navToggle.contains(e.target) && !navMenu.contains(e.target)) {
            navMenu.classList.remove('active');
            navToggle.classList.remove('active');
        }
    });

    // Header scroll effect
    window.addEventListener('scroll', function() {
        const header = document.querySelector('.header');
        if (window.scrollY > 100) {
            header.style.background = 'rgba(255, 255, 255, 0.98)';
            header.style.boxShadow = '0 4px 6px -1px rgb(0 0 0 / 0.1)';
        } else {
            header.style.background = 'rgba(255, 255, 255, 0.95)';
            header.style.boxShadow = 'none';
        }
    });

    // Ensure reseller button works
    const resellerBtn = document.querySelector('.reseller-btn');
    if (resellerBtn) {
        resellerBtn.addEventListener('click', function(e) {
            // Make sure the link works
            const href = this.getAttribute('href');
            if (href && href !== '#') {
                window.location.href = href;
            }
        });
    }
}

// Chat system functionality
function initializeChatSystem() {
    // Open chat modal
    liveChatBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            openChatModal();
        });
    });

    // Close chat modal
    chatClose.addEventListener('click', closeChatModal);

    // Minimize chat modal
    chatMinimize.addEventListener('click', minimizeChatModal);

    // Close modal when clicking outside
    chatModal.addEventListener('click', function(e) {
        if (e.target === chatModal) {
            closeChatModal();
        }
    });

    // Send message functionality
    sendMessageBtn.addEventListener('click', sendMessage);
    chatInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    // Quick reply functionality
    quickReplies.addEventListener('click', function(e) {
        if (e.target.classList.contains('quick-reply')) {
            const message = e.target.getAttribute('data-message');
            chatInput.value = message;
            sendMessage();
        }
    });

    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && chatModal.style.display === 'block') {
            closeChatModal();
        }
    });

    // Auto-resize input
    chatInput.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.min(this.scrollHeight, 100) + 'px';
    });
}

// Initialize chat bubble
function initializeChatBubble() {
    chatBubble.addEventListener('click', function() {
        openChatModal();
        hideNotification();
    });

    // Show notification after delay
    setTimeout(() => {
        showNotification();
    }, 5000);
}

function showNotification() {
    chatNotification.style.display = 'flex';
    chatBubble.classList.add('has-notification');
}

function hideNotification() {
    chatNotification.style.display = 'none';
    chatBubble.classList.remove('has-notification');
}

function showWelcomeMessage() {
    // Show welcome message after a delay
    setTimeout(() => {
        if (!chatActive) {
            showNotification();
        }
    }, 3000);
}

function openChatModal() {
    chatModal.style.display = 'block';
    chatInput.focus();
    hideNotification();

    if (!chatActive) {
        chatActive = true;
        startChatSession();
    }

    // Hide quick replies after first interaction
    if (messageCount > 0) {
        quickReplies.style.display = 'none';
    }
}

function closeChatModal() {
    chatModal.style.display = 'none';
    if (chatTimeout) {
        clearTimeout(chatTimeout);
    }
}

function minimizeChatModal() {
    chatModal.style.display = 'none';
    showNotification();
}

function startChatSession() {
    // Show typing indicator
    showTypingIndicator();

    // Simulate agent connection
    setTimeout(() => {
        hideTypingIndicator();
        addBotMessage("🤖 Hi there! I'm your BadBoyz IPTV assistant. I'm here to help you with:");

        setTimeout(() => {
            addBotMessage("• Pricing and plan comparisons\n• Device compatibility and setup\n• Free trial requests\n• Technical support\n• General questions about our service");

            setTimeout(() => {
                addBotMessage("What would you like to know about our IPTV service? You can use the quick replies below or type your question! 😊");
            }, 1500);
        }, 1000);
    }, 2000);

    // Set timeout for human agent escalation
    chatTimeout = setTimeout(() => {
        if (chatActive && messageCount > 3) {
            showTypingIndicator();
            setTimeout(() => {
                hideTypingIndicator();
                addBotMessage("For more detailed assistance, I'll connect you with our Telegram support team where our specialists can help you immediately! 🚀");
                setTimeout(() => {
                    addTelegramButton();
                }, 2000);
            }, 1500);
        }
    }, CHAT_TIMEOUT);
}

function sendMessage() {
    const message = chatInput.value.trim();
    if (!message || isTyping) return;

    addUserMessage(message);
    chatInput.value = '';
    chatInput.style.height = 'auto';
    messageCount++;

    // Hide quick replies after first message
    if (messageCount === 1) {
        quickReplies.style.display = 'none';
    }

    // Add to conversation context
    conversationContext.push({ role: 'user', message: message });

    // Show typing indicator and process response
    showTypingIndicator();
    setTimeout(() => {
        handleAdvancedBotResponse(message);
    }, 1500 + Math.random() * 1500); // Random delay between 1.5-3 seconds
}

function addUserMessage(message) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'chat-message user';
    messageDiv.innerHTML = `
        <div class="message-avatar">
            <i class="fas fa-user"></i>
        </div>
        <div class="message-content">
            <div class="message-bubble">
                <p>${escapeHtml(message)}</p>
            </div>
            <div class="message-time">${getCurrentTime()}</div>
        </div>
    `;
    chatMessages.appendChild(messageDiv);
    scrollToBottom();
}

function addBotMessage(message, isLoading = false) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'chat-message bot';

    messageDiv.innerHTML = `
        <div class="message-avatar">
            <i class="fas fa-robot"></i>
        </div>
        <div class="message-content">
            <div class="message-bubble">
                <p>${isLoading ? `<span class="loading">${message}</span>` : formatMessage(message)}</p>
            </div>
            <div class="message-time">${getCurrentTime()}</div>
        </div>
    `;

    chatMessages.appendChild(messageDiv);
    scrollToBottom();

    // Add to conversation context
    conversationContext.push({ role: 'bot', message: message });
}

function showTypingIndicator() {
    isTyping = true;
    typingIndicator.style.display = 'flex';
    scrollToBottom();
}

function hideTypingIndicator() {
    isTyping = false;
    typingIndicator.style.display = 'none';
}

function getCurrentTime() {
    const now = new Date();
    return now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
}

function formatMessage(message) {
    // Convert line breaks to <br> tags and format lists
    return message
        .replace(/\n/g, '<br>')
        .replace(/•/g, '•')
        .replace(/(\d+\.\s)/g, '<strong>$1</strong>');
}

function handleAdvancedBotResponse(userMessage) {
    hideTypingIndicator();
    const lowerMessage = userMessage.toLowerCase();
    let response = '';
    let followUp = '';

    // Advanced keyword analysis and contextual responses
    if (lowerMessage.includes('price') || lowerMessage.includes('cost') || lowerMessage.includes('plan') || lowerMessage.includes('pricing')) {
        response = generatePricingResponse(lowerMessage);
    } else if (lowerMessage.includes('trial') || lowerMessage.includes('test') || lowerMessage.includes('free') ||
               lowerMessage.includes('demo') || lowerMessage.includes('try') || lowerMessage.includes('sample') ||
               lowerMessage.includes('preview') || lowerMessage.includes('24 hour') || lowerMessage.includes('24-hour')) {
        response = generateTrialResponse();
    } else if (lowerMessage.includes('device') || lowerMessage.includes('compatible') || lowerMessage.includes('install') || lowerMessage.includes('setup')) {
        response = generateDeviceResponse(lowerMessage);
    } else if (lowerMessage.includes('channel') || lowerMessage.includes('tv') || lowerMessage.includes('content')) {
        response = generateChannelResponse(lowerMessage);
    } else if (lowerMessage.includes('quality') || lowerMessage.includes('hd') || lowerMessage.includes('4k')) {
        response = generateQualityResponse();
    } else if (lowerMessage.includes('support') || lowerMessage.includes('help') || lowerMessage.includes('problem') || lowerMessage.includes('issue')) {
        response = generateSupportResponse();
    } else if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('hey')) {
        response = generateGreetingResponse();
    } else if (lowerMessage.includes('thank') || lowerMessage.includes('thanks')) {
        response = "You're very welcome! 😊 Is there anything else I can help you with regarding our IPTV service?";
    } else if (lowerMessage.includes('bye') || lowerMessage.includes('goodbye')) {
        response = "Thank you for chatting with BadBoyz IPTV! Feel free to reach out anytime. Have a great day! 👋";
    } else {
        response = generateContextualResponse(userMessage);
    }

    addBotMessage(response);

    // Add follow-up suggestions based on context
    if (followUp) {
        setTimeout(() => {
            addBotMessage(followUp);
        }, 2000);
    }

    // Suggest human support after several interactions
    if (messageCount >= 4 && !userSession.hasTrialRequested) {
        setTimeout(() => {
            addBotMessage("Would you like me to connect you with our human support team for personalized assistance? They can help with account setup, technical issues, and special offers! 🎯");
            setTimeout(() => {
                addTelegramButton();
            }, 2000);
        }, 3000);
    }
}

function addTelegramButton() {
    const buttonDiv = document.createElement('div');
    buttonDiv.className = 'chat-message bot';
    buttonDiv.innerHTML = `
        <div class="message-avatar">
            <i class="fas fa-robot"></i>
        </div>
        <div class="message-content">
            <div class="message-bubble">
                <p>🚀 Ready for human support? Our specialists are standing by!</p>
                <button onclick="redirectToTelegram()" class="btn btn-primary" style="margin-top: 10px; width: 100%;">
                    <i class="fab fa-telegram"></i> Connect with Support Team
                </button>
            </div>
            <div class="message-time">${getCurrentTime()}</div>
        </div>
    `;
    chatMessages.appendChild(buttonDiv);
    scrollToBottom();
}

// Free trial request function
function requestFreeTrial() {
    openChatModal();
    setTimeout(() => {
        addUserMessage("I want to request a free trial");
        setTimeout(() => {
            showTypingIndicator();
            setTimeout(() => {
                hideTypingIndicator();
                addBotMessage(generateTrialResponse());
            }, 2000);
        }, 500);
    }, 500);
}

function redirectToTelegram() {
    window.open(TELEGRAM_GROUP_URL, '_blank');
    closeChatModal();
}

function addTrialRedirectButton() {
    const buttonDiv = document.createElement('div');
    buttonDiv.className = 'chat-message bot';
    buttonDiv.innerHTML = `
        <div class="message-avatar">
            <i class="fas fa-robot"></i>
        </div>
        <div class="message-content">
            <div class="message-bubble">
                <p>🎯 Ready to start your free trial?</p>
                <button onclick="redirectToFreeTrial()" class="btn btn-primary" style="margin-top: 10px; width: 100%; background: linear-gradient(135deg, #ff6b35, #ff8c42);">
                    <i class="fas fa-rocket"></i> Start Free Trial Now
                </button>
                <p style="margin-top: 10px; font-size: 0.9em; color: #666; text-align: center;">
                    No credit card required • Instant activation
                </p>
            </div>
            <div class="message-time">${getCurrentTime()}</div>
        </div>
    `;
    chatMessages.appendChild(buttonDiv);
    scrollToBottom();
}

function redirectToFreeTrial() {
    // Close chat modal first
    closeChatModal();

    // Add a small delay then redirect
    setTimeout(() => {
        window.location.href = 'free-trial.html';
    }, 300);
}

// Response generation functions
function generatePricingResponse(message) {
    const plans = knowledgeBase.pricing;
    let response = "💰 Here are our current pricing plans:\n\n";

    Object.keys(plans).forEach(plan => {
        const planData = plans[plan];
        response += `🔹 **${plan.toUpperCase()}** - ${planData.price}/month\n`;
        response += `   • ${planData.channels} channels\n`;
        response += `   • ${planData.quality} quality\n`;
        response += `   • ${planData.devices} simultaneous connections\n\n`;
    });

    response += "All plans include 99.99% uptime guarantee and 24/7 support! Which plan interests you most? 🤔";
    return response;
}

function generateTrialResponse() {
    userSession.hasTrialRequested = true;

    // Add trial redirect button after the message
    setTimeout(() => {
        addTrialRedirectButton();
    }, 2000);

    return "🆓 **FREE 24-HOUR TRIAL AVAILABLE!**\n\nYes! We offer a completely free 24-hour trial with:\n• ✅ Full access to 20,000+ channels\n• ✅ HD/4K streaming quality\n• ✅ 50,000+ VOD movies & shows\n• ✅ No credit card required\n• ✅ No commitments or hidden fees\n• ✅ Instant activation\n\nI'll redirect you to our free trial page where you can sign up in just 30 seconds! 🚀";
}

function generateDeviceResponse(message) {
    let response = "📱 **BadBoyz IPTV works on ALL devices!**\n\n";

    if (message.includes('firestick') || message.includes('fire tv')) {
        response += "🔥 **Amazon Fire Stick/TV Setup:**\n";
        response += knowledgeBase.installation.firestick + "\n\n";
        userSession.deviceType = 'firestick';
    } else if (message.includes('android')) {
        response += "🤖 **Android Setup:**\n";
        response += knowledgeBase.installation.android + "\n\n";
        userSession.deviceType = 'android';
    } else if (message.includes('ios') || message.includes('iphone') || message.includes('ipad')) {
        response += "🍎 **iOS Setup:**\n";
        response += knowledgeBase.installation.ios + "\n\n";
        userSession.deviceType = 'ios';
    } else {
        response += "**Supported devices:**\n";
        response += "📺 Smart TVs (Samsung, LG, Sony, Android TV)\n";
        response += "📱 Mobile (iOS, Android, Windows)\n";
        response += "💻 Computers (Windows, Mac, Linux)\n";
        response += "🔥 Streaming (Fire Stick, Roku, Apple TV)\n";
        response += "📦 Set-top boxes (MAG, Formuler, Dreambox)\n";
        response += "🎮 Gaming consoles (Xbox, PlayStation)\n\n";
    }

    response += "Need specific setup instructions for your device? Just let me know which one! 😊";
    return response;
}

function generateChannelResponse(message) {
    let response = "📺 **Channel & Content Information:**\n\n";
    response += "🌟 **12,000+ Live Channels** including:\n";
    response += "• 🏈 Sports (ESPN, Fox Sports, NFL Network, NBA TV)\n";
    response += "• 🎬 Movies (HBO, Showtime, Starz, Cinemax)\n";
    response += "• 📰 News (CNN, Fox News, BBC, MSNBC)\n";
    response += "• 🌍 International (Spanish, Arabic, Indian, European)\n";
    response += "• 👶 Kids (Disney, Cartoon Network, Nickelodeon)\n\n";
    response += "🎭 **25,000+ VOD Library** with latest movies and TV series\n";
    response += "📅 **7-day Catch-up TV** (Premium plan)\n";
    response += "📋 **Electronic Program Guide (EPG)** for easy browsing\n\n";
    response += "Looking for specific channels or content? Let me know! 🔍";
    return response;
}

function generateQualityResponse() {
    return "🎥 **Streaming Quality:**\n\n• **4K Ultra HD** - Premium content\n• **Full HD 1080p** - Most channels\n• **HD 720p** - Standard quality\n• **Adaptive streaming** - Adjusts to your internet speed\n\n✨ **Features:**\n• Buffer-free streaming\n• 99.99% uptime guarantee\n• Multiple server locations\n• Anti-freeze technology\n\nYour viewing experience will be crystal clear! 🌟";
}

function generateSupportResponse() {
    return "🛠️ **24/7 Support Available:**\n\n• 💬 Live chat (right here!)\n• 📱 Telegram group (instant response)\n• 📧 Email support\n• 📞 Phone support\n• 🎥 Video tutorials\n• 📖 Setup guides\n\nOur average response time is under 2 minutes! How can I help you today? 😊";
}

function generateGreetingResponse() {
    const greetings = [
        "Hello! 👋 Welcome to BadBoyz IPTV! How can I help you today?",
        "Hi there! 😊 Thanks for choosing BadBoyz IPTV. What can I assist you with?",
        "Hey! 🎉 Great to see you here. What would you like to know about our IPTV service?"
    ];
    return greetings[Math.floor(Math.random() * greetings.length)];
}

function generateContextualResponse(message) {
    const contextualResponses = [
        "That's an interesting question! 🤔 Let me help you with that. Could you be more specific about what you'd like to know?",
        "I'd be happy to help! 😊 Could you tell me more about what you're looking for?",
        "Great question! 👍 To give you the best answer, could you provide a bit more detail?",
        "I want to make sure I give you the right information! 🎯 What specific aspect would you like to know more about?"
    ];
    return contextualResponses[Math.floor(Math.random() * contextualResponses.length)];
}

function scrollToBottom() {
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Check support availability based on time
function checkSupportAvailability() {
    const now = new Date();
    const currentHour = now.getHours();
    
    if (currentHour < SUPPORT_AVAILABLE_HOURS.start || currentHour >= SUPPORT_AVAILABLE_HOURS.end) {
        // Outside support hours - show different message
        const originalBotMessage = chatMessages.querySelector('.chat-message.bot p');
        if (originalBotMessage) {
            originalBotMessage.textContent = "Hello! Our support team is currently offline. Please join our Telegram group for 24/7 community support, or leave a message and we'll get back to you during business hours (9 AM - 9 PM).";
        }
    }
}

// Smooth scrolling for navigation links
function initializeSmoothScrolling() {
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const headerHeight = document.querySelector('.header').offsetHeight;
                const targetPosition = targetSection.offsetTop - headerHeight;
                
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
}

// Initialize animations and scroll effects
function initializeAnimations() {
    // Intersection Observer for fade-in animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animatedElements = document.querySelectorAll('.feature-card, .pricing-card, .contact-card');
    animatedElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
}

// Pricing plan selection
function selectPlan(planType) {
    const plans = {
        basic: {
            name: 'Basic Plan',
            price: '$25/month',
            features: '5,000+ channels, HD quality, 2 devices'
        },
        standard: {
            name: 'Standard Plan',
            price: '$30/month',
            features: '8,000+ channels, Full HD & 4K, 4 devices, VOD library'
        },
        premium: {
            name: 'Premium Plan',
            price: '$35/month',
            features: '12,000+ channels, Ultra HD & 4K, 6 devices, VOD library, Catch-up TV'
        }
    };

    const selectedPlan = plans[planType];
    
    // Show confirmation or redirect to payment
    if (confirm(`You selected the ${selectedPlan.name} (${selectedPlan.price})\n\nFeatures: ${selectedPlan.features}\n\nWould you like to proceed? You'll be redirected to our Telegram group to complete the subscription.`)) {
        // In a real implementation, this would redirect to a payment processor
        // For now, redirect to Telegram for manual processing
        window.open(`${TELEGRAM_GROUP_URL}?text=I want to subscribe to the ${selectedPlan.name} (${selectedPlan.price})`, '_blank');
    }
}

// Error handling
window.addEventListener('error', function(e) {
    console.error('JavaScript error:', e.error);
    // In production, you might want to send this to an error tracking service
});

// Performance optimization - lazy loading for images
function initializeLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });

    images.forEach(img => imageObserver.observe(img));
}

// Initialize lazy loading if there are images
if (document.querySelectorAll('img[data-src]').length > 0) {
    initializeLazyLoading();
}

// Service Worker registration for PWA capabilities (optional)
if ('serviceWorker' in navigator) {
    window.addEventListener('load', function() {
        navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
                console.log('ServiceWorker registration successful');
            })
            .catch(function(err) {
                console.log('ServiceWorker registration failed');
            });
    });
}

// Analytics tracking (placeholder for Google Analytics or similar)
function trackEvent(category, action, label) {
    // Placeholder for analytics tracking
    console.log('Event tracked:', category, action, label);
    
    // Example for Google Analytics:
    // gtag('event', action, {
    //     event_category: category,
    //     event_label: label
    // });
}

// Track pricing plan clicks
document.addEventListener('click', function(e) {
    if (e.target.closest('.pricing-card .btn')) {
        const planCard = e.target.closest('.pricing-card');
        const planName = planCard.querySelector('h3').textContent;
        trackEvent('Pricing', 'Plan Selected', planName);
    }
    
    if (e.target.closest('#liveChatBtn, #contactChatBtn')) {
        trackEvent('Support', 'Chat Opened', 'Live Chat');
    }
});

// Accessibility improvements
document.addEventListener('keydown', function(e) {
    // Skip to main content with Tab key
    if (e.key === 'Tab' && e.target === document.body) {
        const mainContent = document.querySelector('main') || document.querySelector('.hero');
        if (mainContent) {
            mainContent.focus();
        }
    }
});

// Add focus indicators for keyboard navigation
document.addEventListener('keydown', function(e) {
    if (e.key === 'Tab') {
        document.body.classList.add('keyboard-navigation');
    }
});

document.addEventListener('mousedown', function() {
    document.body.classList.remove('keyboard-navigation');
});

// Utility function to debounce scroll events
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Optimized scroll handler
const optimizedScrollHandler = debounce(function() {
    // Any scroll-based functionality can be added here
}, 100);

window.addEventListener('scroll', optimizedScrollHandler);

// Export functions for testing (if needed)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        selectPlan,
        redirectToTelegram,
        trackEvent
    };
}
