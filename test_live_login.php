<?php
/**
 * Test Live Login - Simulate the exact login process from the reseller panel
 */

// Set headers to match the frontend request
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle CORS preflight
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once 'config/database.php';

// Start session
session_start();

echo "<!DOCTYPE html>";
echo "<html><head><title>Live Login Test</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }";
echo ".container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }";
echo ".success { background: #d4edda; padding: 15px; border-radius: 4px; margin: 10px 0; color: #155724; }";
echo ".error { background: #f8d7da; padding: 15px; border-radius: 4px; margin: 10px 0; color: #721c24; }";
echo ".info { background: #d1ecf1; padding: 15px; border-radius: 4px; margin: 10px 0; color: #0c5460; }";
echo "button { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; background: #007bff; color: white; }";
echo "pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; font-size: 12px; }";
echo "#results { margin-top: 20px; }";
echo "</style></head><body>";

echo "<div class='container'>";
echo "<h1>🔐 Live Login Test</h1>";
echo "<p>Testing the exact same login process used by the reseller panel</p>";

// Test 1: Database connection
echo "<h3>Step 1: Database Connection</h3>";
try {
    $db = getDB();
    echo "<div class='success'>✅ Database connected successfully</div>";
} catch (Exception $e) {
    echo "<div class='error'>❌ Database connection failed: " . $e->getMessage() . "</div>";
    echo "</div></body></html>";
    exit;
}

// Test 2: Check admin user
echo "<h3>Step 2: Admin User Status</h3>";
try {
    $stmt = $db->prepare("SELECT id, username, password_hash, status, auth_token, token_expires FROM reseller_admins WHERE username = ?");
    $stmt->execute(['Badboyztv']);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<div class='success'>✅ Admin user found</div>";
        echo "<pre>";
        echo "ID: " . $admin['id'] . "\n";
        echo "Username: " . $admin['username'] . "\n";
        echo "Status: " . $admin['status'] . "\n";
        echo "Has Password Hash: " . (strlen($admin['password_hash']) > 10 ? 'Yes' : 'No') . "\n";
        echo "Current Token: " . ($admin['auth_token'] ? substr($admin['auth_token'], 0, 20) . '...' : 'None') . "\n";
        echo "Token Expires: " . ($admin['token_expires'] ?: 'None') . "\n";
        echo "</pre>";
    } else {
        echo "<div class='error'>❌ Admin user not found</div>";
        echo "</div></body></html>";
        exit;
    }
} catch (Exception $e) {
    echo "<div class='error'>❌ Error checking admin: " . $e->getMessage() . "</div>";
    echo "</div></body></html>";
    exit;
}

// Test 3: Password verification
echo "<h3>Step 3: Password Verification</h3>";
if (verifyPassword('Skyblue14!', $admin['password_hash'])) {
    echo "<div class='success'>✅ Password verification successful</div>";
} else {
    echo "<div class='error'>❌ Password verification failed - fixing...</div>";
    
    $newHash = hashPassword('Skyblue14!');
    $stmt = $db->prepare("UPDATE reseller_admins SET password_hash = ? WHERE username = ?");
    if ($stmt->execute([$newHash, 'Badboyztv'])) {
        echo "<div class='success'>✅ Password hash updated</div>";
        $admin['password_hash'] = $newHash;
    } else {
        echo "<div class='error'>❌ Failed to update password</div>";
        echo "</div></body></html>";
        exit;
    }
}

// Test 4: Simulate actual login API call
echo "<h3>Step 4: Simulate Login API Call</h3>";

// Clear any existing session
session_destroy();
session_start();

try {
    // Simulate the exact login process from auth.php
    $username = 'Badboyztv';
    $password = 'Skyblue14!';
    
    // Get fresh admin data
    $stmt = $db->prepare("
        SELECT id, username, password_hash, full_name, role, status, 
               login_attempts, locked_until 
        FROM reseller_admins 
        WHERE username = ?
    ");
    $stmt->execute([$username]);
    $admin = $stmt->fetch();
    
    if (!$admin) {
        echo "<div class='error'>❌ Admin not found during login simulation</div>";
        echo "</div></body></html>";
        exit;
    }
    
    // Check account status
    if ($admin['status'] !== 'active') {
        echo "<div class='error'>❌ Account is not active: " . $admin['status'] . "</div>";
        echo "</div></body></html>";
        exit;
    }
    
    // Verify password
    if (!verifyPassword($password, $admin['password_hash'])) {
        echo "<div class='error'>❌ Password verification failed during login</div>";
        echo "</div></body></html>";
        exit;
    }
    
    // Generate token
    $token = generateToken();
    
    // Update database
    $stmt = $db->prepare("
        UPDATE reseller_admins 
        SET login_attempts = 0, locked_until = NULL, last_login = NOW(), auth_token = ?, token_expires = DATE_ADD(NOW(), INTERVAL 24 HOUR)
        WHERE id = ?
    ");
    $stmt->execute([$token, $admin['id']]);
    
    // Set session
    $_SESSION['admin_id'] = $admin['id'];
    $_SESSION['username'] = $admin['username'];
    $_SESSION['role'] = $admin['role'];
    $_SESSION['token'] = $token;
    $_SESSION['login_time'] = time();
    
    echo "<div class='success'>✅ Login simulation successful!</div>";
    echo "<pre>";
    echo "Token: " . substr($token, 0, 30) . "...\n";
    echo "User ID: " . $admin['id'] . "\n";
    echo "Username: " . $admin['username'] . "\n";
    echo "Role: " . $admin['role'] . "\n";
    echo "Session ID: " . session_id() . "\n";
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<div class='error'>❌ Login simulation failed: " . $e->getMessage() . "</div>";
    echo "</div></body></html>";
    exit;
}

echo "<h3>Step 5: Test API Endpoints</h3>";
echo "<button onclick='testLoginAPI()'>Test Login API</button>";
echo "<button onclick='testDashboardAPI()'>Test Dashboard API</button>";
echo "<div id='results'></div>";

echo "<h3>Quick Links</h3>";
echo "<p><a href='https://badboyzmedia.org/reseller-panel.html' target='_blank'>🔗 Open Live Reseller Panel</a></p>";
echo "<p><a href='emergency_login.php' target='_blank'>🚨 Emergency Login</a></p>";

echo "<script>";
echo "async function testLoginAPI() {";
echo "  const results = document.getElementById('results');";
echo "  try {";
echo "    const response = await fetch('/api/auth/login', {";
echo "      method: 'POST',";
echo "      headers: { 'Content-Type': 'application/json' },";
echo "      body: JSON.stringify({ username: 'Badboyztv', password: 'Skyblue14!' })";
echo "    });";
echo "    const data = await response.json();";
echo "    results.innerHTML = '<div class=\"' + (response.ok ? 'success' : 'error') + '\"><h4>Login API Result:</h4><pre>' + JSON.stringify(data, null, 2) + '</pre></div>';";
echo "    if (response.ok && data.success) {";
echo "      localStorage.setItem('authToken', data.data.token);";
echo "      console.log('Token stored:', data.data.token);";
echo "    }";
echo "  } catch (error) {";
echo "    results.innerHTML = '<div class=\"error\"><h4>Login API Error:</h4><pre>' + error.message + '</pre></div>';";
echo "  }";
echo "}";

echo "async function testDashboardAPI() {";
echo "  const results = document.getElementById('results');";
echo "  const token = localStorage.getItem('authToken');";
echo "  if (!token) {";
echo "    results.innerHTML = '<div class=\"error\">No token found. Please test login first.</div>';";
echo "    return;";
echo "  }";
echo "  try {";
echo "    const response = await fetch('/api/dashboard', {";
echo "      method: 'GET',";
echo "      headers: { 'Content-Type': 'application/json', 'Authorization': 'Bearer ' + token }";
echo "    });";
echo "    const data = await response.json();";
echo "    results.innerHTML += '<div class=\"' + (response.ok ? 'success' : 'error') + '\"><h4>Dashboard API Result:</h4><pre>' + JSON.stringify(data, null, 2) + '</pre></div>';";
echo "  } catch (error) {";
echo "    results.innerHTML += '<div class=\"error\"><h4>Dashboard API Error:</h4><pre>' + error.message + '</pre></div>';";
echo "  }";
echo "}";
echo "</script>";

echo "</div></body></html>";
?>
