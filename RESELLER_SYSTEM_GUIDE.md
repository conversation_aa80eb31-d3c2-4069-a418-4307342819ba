# BadBoyz IPTV Reseller System Guide

## 🔐 Admin Login Credentials

**Username:** `Badboyztv`  
**Password:** `Skyblue14!`

## 🚀 Features Overview

### 1. **Secure Authentication**
- Only authorized admin can access the reseller panel
- Session management with login time tracking
- Proper error handling for invalid credentials

### 2. **Enhanced Customer Management**
- **Register New Users**: Complete user registration with account creation
- **Add Existing Customers**: Add customers who already have accounts
- **Detailed Customer Information**:
  - Full name, email, username, phone
  - Subscription plan and status
  - Due dates with visual warnings (red for expired, yellow for due soon)
  - Payment history and total paid
  - Device limits and usage

### 3. **Smart Due Date Tracking**
- **Visual Indicators**: 
  - 🔴 Red text for expired subscriptions
  - 🟡 Yellow text for subscriptions due within 7 days
  - ⚫ Normal text for active subscriptions
- **Days Remaining**: Shows exact days left or days overdue
- **Status Badges**: Active, Expired, Due Soon, Pending, Suspended

### 4. **User Registration System**
- **Complete Registration Form**:
  - Full name and contact information
  - Username and temporary password
  - Subscription plan selection (Basic $25, Standard $30, Premium $35)
  - Duration selection (1, 3, 6, or 12 months)
  - Device limit configuration
- **Automatic Calculations**:
  - Due date calculation based on duration
  - Total payment calculation
  - Registration date tracking

### 5. **Customer Actions**
- **View Details**: Complete customer information popup
- **Edit Customer**: Modify customer information
- **Extend Subscription**: Add months to existing subscription
- **Bulk Operations**: Extend, suspend, activate, or delete multiple customers

## 📊 Dashboard Statistics

The system automatically calculates and displays:
- Total customers
- Active customers
- Expired customers  
- Customers due soon (within 7 days)

## 🎯 How to Use

### Login
1. Go to `http://localhost:8000/reseller-panel.html`
2. Enter credentials: `Badboyztv` / `Skyblue14!`
3. Click "Login to Panel"

### Register New User
1. Click "Register New User" button
2. Fill in all required information
3. Select subscription plan and duration
4. Set temporary password
5. Click "Register User"
6. User details will be displayed in success message

### Manage Existing Customers
1. Go to "Customer Management" section
2. View all customers with their due dates
3. Use action buttons to:
   - 👁️ View complete customer details
   - ✏️ Edit customer information
   - 📅 Extend subscription
4. Monitor due dates and payment status

### Subscription Management
1. Go to "Subscription Management" section
2. Use checkboxes to select multiple customers
3. Perform bulk actions:
   - Extend subscriptions
   - Suspend/activate accounts
   - Delete accounts (with confirmation)

## 💡 Key Benefits

1. **Professional Management**: Complete customer lifecycle management
2. **Financial Tracking**: Monitor payments and revenue per customer
3. **Proactive Management**: Visual warnings for upcoming renewals
4. **Bulk Operations**: Efficient management of multiple customers
5. **Secure Access**: Protected admin panel with proper authentication
6. **User-Friendly**: Intuitive interface with clear visual indicators

## 🔧 Technical Features

- **Data Persistence**: Customer data stored in browser localStorage
- **Real-time Updates**: Automatic refresh of statistics and tables
- **Responsive Design**: Works on desktop and mobile devices
- **Form Validation**: Prevents duplicate usernames/emails
- **Error Handling**: User-friendly error messages and notifications

## 📱 Mobile Compatibility

The reseller panel is fully responsive and works on:
- Desktop computers
- Tablets
- Mobile phones
- All modern browsers

---

**Note**: This is a demo system using localStorage for data storage. In a production environment, this would be connected to a proper database with server-side authentication and data management.
