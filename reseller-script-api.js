/**
 * BadBoyz IPTV Reseller Panel - API Version
 * Uses PHP backend instead of localStorage
 */

// API Configuration
const API_BASE_URL = '/api';

// Function to get current auth token (always fresh from localStorage)
function getAuthToken() {
    return localStorage.getItem('authToken');
}

// ===== GLOBAL FUNCTIONS FOR HTML ONCLICK EVENTS =====
// These must be declared at the top level to be accessible from HTML

window.showAddCustomerModal = function() {
    console.log('Opening add customer modal...');
    console.log('Auth token available:', !!authToken);

    const modal = document.getElementById('registerUserModal');
    if (modal) {
        modal.style.display = 'block';
        console.log('Modal opened successfully');
    } else {
        console.error('Modal not found: registerUserModal');
    }
};

window.toggleSidebar = function() {
    const sidebar = document.querySelector('.sidebar');
    const mainContent = document.querySelector('.main-content');

    if (sidebar) {
        sidebar.classList.toggle('collapsed');
    }
    if (mainContent) {
        mainContent.classList.toggle('sidebar-collapsed');
    }
};

window.closeModal = function(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
    }
};

window.showTab = function(tabName) {
    showSection(tabName);
};

window.showSection = function(sectionName) {
    console.log('Showing section:', sectionName);

    // Hide all content sections
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => {
        section.classList.remove('active');
        section.style.display = 'none';
    });

    // Show selected section
    const targetSection = document.getElementById(sectionName + 'Section');
    if (targetSection) {
        targetSection.classList.add('active');
        targetSection.style.display = 'block';
        console.log('Section shown:', sectionName);
    } else {
        console.error('Section not found:', sectionName + 'Section');
    }

    // Update active navigation link
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => link.classList.remove('active'));

    // Find and activate the corresponding nav link
    const activeLink = document.querySelector(`[onclick*="showSection('${sectionName}')"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }

    // Update page title
    const pageTitle = document.getElementById('pageTitle');
    if (pageTitle) {
        const titles = {
            'overview': 'Dashboard Overview',
            'customers': 'Customer Management',
            'subscriptions': 'Subscriptions',
            'sales': 'Sales & Earnings',
            'credits': 'Credits',
            'tools': 'Tools',
            'support': 'Support',
            'settings': 'Settings'
        };
        pageTitle.textContent = titles[sectionName] || 'Dashboard';
    }

    // Load data for specific sections
    if (sectionName === 'customers') {
        loadCustomers();
    } else if (sectionName === 'overview') {
        loadDashboardData();
    }
};

window.logout = function() {
    handleLogout();
};

// Debug function to test authentication (call from browser console)
window.testAuth = async function() {
    console.log('=== Authentication Test ===');
    const currentToken = getAuthToken();
    console.log('Current auth token:', currentToken);
    console.log('Token in localStorage:', localStorage.getItem('authToken'));

    try {
        // First test debug endpoint
        console.log('Testing debug endpoint...');
        const debugResponse = await apiRequest('/debug');
        console.log('Debug response:', debugResponse);

        // Then test auth verify
        console.log('Testing auth verify...');
        const response = await apiRequest('/auth/verify');
        console.log('Auth verify response:', response);
        return response;
    } catch (error) {
        console.error('Auth verify error:', error);
        return error;
    }
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    checkAuthStatus();
    setupEventListeners();
});

// API Helper Functions
async function apiRequest(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const config = {
        headers: {
            'Content-Type': 'application/json',
            ...options.headers
        },
        ...options
    };
    
    // Add auth token if available (always get fresh token)
    const currentToken = getAuthToken();
    if (currentToken) {
        config.headers['Authorization'] = `Bearer ${currentToken}`;
    }
    
    try {
        console.log('API Request:', url, config);
        const response = await fetch(url, config);

        let data;
        try {
            data = await response.json();
        } catch (jsonError) {
            console.error('Failed to parse JSON response:', jsonError);
            throw new Error(`API returned invalid JSON (Status: ${response.status})`);
        }

        console.log('API Response:', response.status, data);

        if (!response.ok) {
            const errorMsg = data.error || data.message || `HTTP ${response.status}`;
            throw new Error(errorMsg);
        }

        return data;
    } catch (error) {
        console.error('API Error:', error);

        // Handle authentication errors
        if (error.message.includes('401') || error.message.includes('token') || error.message.includes('Authentication')) {
            handleAuthError();
        }

        throw error;
    }
}

function handleAuthError() {
    authToken = null;
    localStorage.removeItem('authToken');
    showLoginForm();
    showNotification('Session expired. Please login again.', 'error');
}

// Authentication Functions
async function checkAuthStatus() {
    console.log('checkAuthStatus() called');
    console.log('Current authToken:', authToken ? authToken.substring(0, 20) + '...' : 'None');

    if (!authToken) {
        console.log('No token found, showing login form');
        showLoginForm();
        return;
    }

    try {
        console.log('Verifying token...');
        const response = await apiRequest('/auth/verify');
        console.log('Token verification response:', response);

        if (response.success) {
            console.log('Token valid, showing dashboard');
            showDashboard();
            loadDashboardData();
        } else {
            console.log('Token invalid, showing login form');
            authToken = null;
            localStorage.removeItem('authToken');
            showLoginForm();
        }
    } catch (error) {
        console.error('Token verification error:', error);
        authToken = null;
        localStorage.removeItem('authToken');
        showLoginForm();
    }
}

async function handleLogin(e) {
    e.preventDefault();
    
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;
    
    if (!username || !password) {
        showNotification('Please enter both username and password', 'error');
        return;
    }
    
    try {
        showLoading(true);
        
        const response = await apiRequest('/auth/login', {
            method: 'POST',
            body: JSON.stringify({ username, password })
        });
        
        if (response.success) {
            authToken = response.data.token;
            localStorage.setItem('authToken', authToken);
            localStorage.setItem('userInfo', JSON.stringify(response.data.user));

            console.log('Login successful, showing dashboard...');
            console.log('Token stored:', authToken.substring(0, 20) + '...');

            // Force dashboard display with delay to ensure DOM is ready
            setTimeout(() => {
                showDashboard();
                loadDashboardData();
                showNotification(`Welcome back, ${response.data.user.full_name}!`, 'success');
            }, 100);
        } else {
            console.error('Login failed:', response);
            showNotification(response.message || 'Login failed', 'error');
        }
    } catch (error) {
        showNotification(error.message, 'error');
        document.getElementById('username').value = '';
        document.getElementById('password').value = '';
    } finally {
        showLoading(false);
    }
}

async function handleLogout() {
    try {
        await apiRequest('/auth/logout', { method: 'POST' });
    } catch (error) {
        console.error('Logout error:', error);
    } finally {
        authToken = null;
        localStorage.removeItem('authToken');
        localStorage.removeItem('userInfo');
        showLoginForm();
        showNotification('Logged out successfully', 'success');
    }
}

// Duplicate function declarations removed - all global functions are declared at the top of the file

// Dashboard Functions
async function loadDashboardData() {
    try {
        console.log('Loading dashboard data...');
        const response = await apiRequest('/dashboard');
        console.log('Dashboard response:', response);

        if (response.success) {
            updateDashboardStats(response.data);
            console.log('Dashboard data updated successfully');
        } else {
            console.error('Dashboard API returned error:', response);
            showNotification(response.message || 'Failed to load dashboard data', 'error');
        }
    } catch (error) {
        console.error('Dashboard load error:', error);

        // Show more detailed error information
        let errorMessage = 'Failed to load dashboard data';
        if (error.message) {
            errorMessage += ': ' + error.message;
        }

        showNotification(errorMessage, 'error');
    }
}

function updateDashboardStats(data) {
    const stats = data.customer_stats;

    // Update stat cards using the actual HTML structure
    const statNumbers = document.querySelectorAll('.stat-number');
    if (statNumbers.length >= 4) {
        // Update based on order in HTML: Total Customers, Active Subscriptions, Monthly Earnings, Available Credits
        statNumbers[0].textContent = stats.total || '0';
        statNumbers[1].textContent = stats.active || '0';

        // Update revenue stats if available
        if (data.revenue_stats) {
            statNumbers[2].textContent = `$${data.revenue_stats.monthly_revenue ? data.revenue_stats.monthly_revenue.toFixed(2) : '0.00'}`;
        }

        // Credits would be updated separately if available
        // statNumbers[3] is for credits
    }

    // Update user name in header
    const userName = document.getElementById('userName');
    if (userName && data.user) {
        userName.textContent = data.user.full_name || 'Admin';
    }
}

// Customer Management Functions
async function loadCustomers(page = 1, search = '', status = '') {
    try {
        showLoading(true);
        
        const params = new URLSearchParams({
            page: page.toString(),
            limit: '20'
        });
        
        if (search) params.append('search', search);
        if (status) params.append('status', status);
        
        const response = await apiRequest(`/customers?${params}`);
        
        if (response.success) {
            displayCustomers(response.data.customers);
            updatePagination(response.data.pagination);
        }
    } catch (error) {
        console.error('Load customers error:', error);
        showNotification('Failed to load customers', 'error');
    } finally {
        showLoading(false);
    }
}

function displayCustomers(customers) {
    const tbody = document.getElementById('customersTableBody');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    
    customers.forEach(customer => {
        const row = createCustomerRow(customer);
        tbody.appendChild(row);
    });
}

function createCustomerRow(customer) {
    const row = document.createElement('tr');
    
    // Calculate days remaining
    let daysRemaining = 0;
    let statusClass = '';
    let statusText = customer.status;
    
    if (customer.subscription) {
        daysRemaining = Math.floor(customer.subscription.days_remaining);
        if (daysRemaining < 0) {
            statusClass = 'text-danger';
            statusText = 'Expired';
        } else if (daysRemaining <= 7) {
            statusClass = 'text-warning';
            statusText = 'Due Soon';
        }
    }
    
    row.innerHTML = `
        <td>
            <div class="customer-info">
                <div class="customer-name">${customer.full_name}</div>
                <div class="customer-email">${customer.email}</div>
            </div>
        </td>
        <td>${customer.username}</td>
        <td>${customer.phone || 'N/A'}</td>
        <td>
            <span class="status-badge status-${customer.status}">${statusText}</span>
        </td>
        <td>
            ${customer.subscription ? `
                <span class="${statusClass}">
                    ${customer.subscription.end_date}
                    ${daysRemaining >= 0 ? `(${daysRemaining} days)` : `(${Math.abs(daysRemaining)} days overdue)`}
                </span>
            ` : 'No subscription'}
        </td>
        <td>
            ${customer.subscription ? customer.subscription.plan_name : 'N/A'}
        </td>
        <td>$${customer.payment_info.total_paid.toFixed(2)}</td>
        <td>${customer.current_devices}/${customer.max_devices}</td>
        <td>
            <div class="action-buttons">
                <button class="btn btn-sm btn-outline" onclick="viewCustomer(${customer.id})" title="View Details">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-sm btn-outline" onclick="editCustomer(${customer.id})" title="Edit">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-outline" onclick="extendSubscription(${customer.id})" title="Extend">
                    <i class="fas fa-calendar-plus"></i>
                </button>
            </div>
        </td>
    `;
    
    return row;
}

// Customer Registration
async function handleRegisterUser(e) {
    e.preventDefault();

    console.log('Starting user registration...');
    console.log('Auth token available:', !!authToken);

    const formData = {
        username: document.getElementById('regUsername').value,
        email: document.getElementById('regEmail').value,
        full_name: document.getElementById('regFullName').value,
        phone: document.getElementById('regPhone').value,
        password: document.getElementById('regPassword').value,
        plan_name: document.getElementById('regPlan').value, // Use plan name instead of ID
        duration_months: parseInt(document.getElementById('regDuration').value),
        max_devices: parseInt(document.getElementById('regDevices').value) || 2
    };

    console.log('Registration data:', formData);

    try {
        showLoading(true);

        const response = await apiRequest('/customers/register', {
            method: 'POST',
            body: JSON.stringify(formData)
        });

        console.log('Registration response:', response);

        if (response.success) {
            closeModal('registerUserModal');
            e.target.reset();
            loadCustomers(); // Reload customer list
            showNotification(`User ${formData.username} registered successfully!`, 'success');
        } else {
            showNotification(response.message || 'Registration failed', 'error');
        }
    } catch (error) {
        console.error('Registration error:', error);
        showNotification(error.message, 'error');
    } finally {
        showLoading(false);
    }
}

// Customer Actions
async function viewCustomer(customerId) {
    try {
        const response = await apiRequest(`/customers/${customerId}`);
        if (response.success) {
            showCustomerDetails(response.data);
        }
    } catch (error) {
        showNotification('Failed to load customer details', 'error');
    }
}

async function extendSubscription(customerId) {
    const months = prompt('How many months to extend?', '1');
    if (!months || isNaN(months) || months <= 0) return;
    
    try {
        const response = await apiRequest(`/customers/${customerId}/extend`, {
            method: 'POST',
            body: JSON.stringify({ months: parseInt(months) })
        });
        
        if (response.success) {
            loadCustomers(); // Reload customer list
            showNotification('Subscription extended successfully!', 'success');
        }
    } catch (error) {
        showNotification(error.message, 'error');
    }
}

// UI Helper Functions
function showLoading(show) {
    const loader = document.getElementById('loadingSpinner');
    if (loader) {
        loader.style.display = show ? 'block' : 'none';
    }
}

function showLoginForm() {
    const loginScreen = document.getElementById('loginScreen');
    const dashboard = document.getElementById('dashboard');
    if (loginScreen) loginScreen.classList.remove('hidden');
    if (dashboard) dashboard.classList.add('hidden');
}

function showDashboard() {
    console.log('showDashboard() called');

    const loginScreen = document.getElementById('loginScreen');
    const dashboard = document.getElementById('dashboard');

    console.log('Login screen element:', loginScreen);
    console.log('Dashboard element:', dashboard);

    if (loginScreen) {
        loginScreen.classList.add('hidden');
        loginScreen.style.display = 'none';
        console.log('Login screen hidden');
    } else {
        console.error('Login screen element not found');
    }

    if (dashboard) {
        dashboard.classList.remove('hidden');
        dashboard.style.display = 'block';
        console.log('Dashboard shown');
    } else {
        console.error('Dashboard element not found');
    }
}

function setupEventListeners() {
    // Login form
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    // Register user form
    const registerForm = document.getElementById('registerUserForm');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegisterUser);
    }
    
    // Logout button (uses onclick in HTML, but we can also add event listener)
    const logoutBtn = document.querySelector('.logout-btn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }
    
    // Load customers when customer management is shown
    const customerTab = document.querySelector('[data-tab="customers"]');
    if (customerTab) {
        customerTab.addEventListener('click', () => loadCustomers());
    }
}

// Utility Functions (continued from previous functions)
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <span>${message}</span>
        <button onclick="this.parentElement.remove()">&times;</button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}



function showCustomerDetails(customer) {
    let details = `
Customer Details:
Name: ${customer.full_name}
Username: ${customer.username}
Email: ${customer.email}
Phone: ${customer.phone || 'N/A'}
Status: ${customer.status}
Devices: ${customer.current_devices}/${customer.max_devices}
Created: ${customer.created_at}
`;

    if (customer.subscription) {
        details += `
Subscription:
Plan: ${customer.subscription.plan_name}
Price: $${customer.subscription.price}
Start Date: ${customer.subscription.start_date}
End Date: ${customer.subscription.end_date}
Days Remaining: ${Math.floor(customer.subscription.days_remaining)}
`;
    }

    details += `
Payment Info:
Total Paid: $${customer.payment_info.total_paid}
Last Payment: ${customer.payment_info.last_payment || 'N/A'}
`;

    alert(details);
}

function updatePagination(pagination) {
    // Update pagination controls if they exist
    const paginationContainer = document.getElementById('pagination');
    if (!paginationContainer) return;

    let paginationHTML = '';

    if (pagination.has_prev) {
        paginationHTML += `<button onclick="loadCustomers(${pagination.current_page - 1})">Previous</button>`;
    }

    paginationHTML += `<span>Page ${pagination.current_page} of ${pagination.total_pages}</span>`;

    if (pagination.has_next) {
        paginationHTML += `<button onclick="loadCustomers(${pagination.current_page + 1})">Next</button>`;
    }

    paginationContainer.innerHTML = paginationHTML;
}

// Initialize when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', function() {
        checkAuthStatus();
        setupEventListeners();
    });
} else {
    checkAuthStatus();
    setupEventListeners();
}
