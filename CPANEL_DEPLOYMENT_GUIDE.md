# 🚀 BadBoyz IPTV - cPanel/InMotion Hosting Deployment Guide

## 📋 Prerequisites

- InMotion Hosting account with cPanel access
- PHP 7.4+ support (most cPanel hosting includes this)
- MySQL database access
- File Manager or FTP access

## 🗄️ Step 1: Database Setup

### 1.1 Create Database in cPanel

1. **Login to cPanel**
2. **Go to "MySQL Databases"**
3. **Create New Database:**
   - Database Name: `badboyz_iptv` (cPane<PERSON> will prefix with your username)
   - Click "Create Database"

4. **Create Database User:**
   - Username: `badboyz_user` (cPanel will prefix with your username)
   - Password: Generate a strong password
   - Click "Create User"

5. **Add User to Database:**
   - Select the user and database
   - Grant "ALL PRIVILEGES"
   - Click "Make Changes"

### 1.2 Import Database Structure

1. **Go to phpMyAdmin** (in cPanel)
2. **Select your database**
3. **Click "Import" tab**
4. **Upload the `database_setup.sql` file**
5. **Click "Go" to execute**

**Note:** The SQL file will create all necessary tables and insert sample data including:
- Admin user: `Badboyztv` / `Skyblue14!`
- Sample customers and subscriptions
- Default subscription plans

## 📁 Step 2: File Upload

### 2.1 Directory Structure

Upload files to your `public_html` directory (or subdirectory) with this structure:

```
public_html/
├── index.html                    # Main website
├── reseller-panel.html          # Reseller panel (updated)
├── free-trial.html              # Free trial page
├── reseller-script-api.js       # New API-based script
├── reseller-styles.css          # Existing styles
├── script.js                    # Main website script
├── styles.css                   # Main website styles
├── free-trial-script.js         # Free trial script
├── free-trial-styles.css        # Free trial styles
├── config/
│   └── database.php             # Database configuration
├── api/
│   ├── index.php                # API router
│   ├── auth.php                 # Authentication endpoints
│   ├── customers.php            # Customer management
│   └── dashboard.php            # Dashboard data
└── logs/                        # Error logs (create this folder)
    └── error.log                # Will be created automatically
```

### 2.2 Update Configuration

**Edit `config/database.php`:**

```php
// Update these lines with your actual cPanel database details:
define('DB_HOST', 'localhost');
define('DB_NAME', 'yourusername_badboyz_iptv');    // Replace 'yourusername'
define('DB_USER', 'yourusername_badboyz_user');    // Replace 'yourusername'  
define('DB_PASS', 'your_database_password');       // Your actual password
```

**Example:**
If your cPanel username is `john123`, your settings would be:
```php
define('DB_NAME', 'john123_badboyz_iptv');
define('DB_USER', 'john123_badboyz_user');
```

## 🔧 Step 3: Update Reseller Panel

### 3.1 Update HTML File

**Edit `reseller-panel.html`** - Change the script reference:

```html
<!-- Replace this line: -->
<script src="reseller-script.js"></script>

<!-- With this: -->
<script src="reseller-script-api.js"></script>
```

### 3.2 Add Loading Spinner (Optional)

Add this to your HTML body for better UX:

```html
<div id="loadingSpinner" style="display: none; position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); z-index: 9999;">
    <div class="spinner">Loading...</div>
</div>
```

## 🔐 Step 4: Security Setup

### 4.1 Create .htaccess Files

**Create `/config/.htaccess`:**
```apache
# Deny access to config files
<Files "*.php">
    Order allow,deny
    Deny from all
</Files>
```

**Create `/logs/.htaccess`:**
```apache
# Deny access to log files
Order allow,deny
Deny from all
```

### 4.2 Set Folder Permissions

- **Folders:** 755 permissions
- **PHP files:** 644 permissions
- **logs/ folder:** 755 permissions (needs write access)

## 🧪 Step 5: Testing

### 5.1 Test Database Connection

Create a temporary test file `test_db.php`:

```php
<?php
require_once 'config/database.php';

try {
    $db = getDB();
    echo "✅ Database connection successful!<br>";
    
    // Test admin login
    $stmt = $db->prepare("SELECT username FROM reseller_admins WHERE username = ?");
    $stmt->execute(['Badboyztv']);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "✅ Admin user found: " . $admin['username'] . "<br>";
    } else {
        echo "❌ Admin user not found<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage();
}
?>
```

**Visit:** `https://yourdomain.com/test_db.php`

**⚠️ Important:** Delete this file after testing!

### 5.2 Test API Endpoints

**Test Authentication:**
```bash
curl -X POST https://yourdomain.com/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"Badboyztv","password":"Skyblue14!"}'
```

### 5.3 Test Reseller Panel

1. **Visit:** `https://yourdomain.com/reseller-panel.html`
2. **Login with:** `Badboyztv` / `Skyblue14!`
3. **Test Features:**
   - Dashboard statistics
   - Customer list loading
   - User registration
   - Customer management

## 🔍 Step 6: Troubleshooting

### Common Issues:

**1. Database Connection Failed**
- Check database credentials in `config/database.php`
- Verify database and user exist in cPanel
- Ensure user has proper privileges

**2. API Endpoints Not Working**
- Check if PHP is enabled
- Verify file permissions
- Check error logs in cPanel

**3. Authentication Issues**
- Verify admin user exists in database
- Check password hash in database
- Clear browser cache and localStorage

**4. Customer Registration Not Working**
- Check database table structure
- Verify API endpoints are accessible
- Check browser console for JavaScript errors

### Error Logs:

- **cPanel Error Logs:** Check in cPanel > Error Logs
- **Application Logs:** Check `/logs/error.log`
- **Browser Console:** Check for JavaScript errors

## 📊 Step 7: Production Optimization

### 7.1 Security Enhancements

1. **Change Default Passwords:**
   ```sql
   UPDATE reseller_admins 
   SET password_hash = '$2y$10$your_new_hash_here' 
   WHERE username = 'Badboyztv';
   ```

2. **Update JWT Secret:**
   - Change `JWT_SECRET` in `config/database.php`

3. **Enable HTTPS:**
   - Use SSL certificate (usually free with InMotion)

### 7.2 Performance Optimization

1. **Enable PHP OPcache** (if available in cPanel)
2. **Use CDN** for static files
3. **Enable Gzip compression**

### 7.3 Backup Strategy

1. **Database Backups:** Use cPanel backup tools
2. **File Backups:** Regular file system backups
3. **Automated Backups:** Set up cron jobs if available

## 🎯 Step 8: Going Live

1. **Remove test files** (`test_db.php`, etc.)
2. **Set `APP_DEBUG = false`** in `config/database.php`
3. **Test all functionality** thoroughly
4. **Monitor error logs** for the first few days
5. **Set up monitoring** for uptime and performance

## 📞 Support

If you encounter issues:

1. **Check Error Logs:** Both cPanel and application logs
2. **Verify Configuration:** Double-check all settings
3. **Test Step by Step:** Isolate the problem
4. **Contact Hosting Support:** For server-related issues

---

**🎉 Congratulations!** Your BadBoyz IPTV reseller system is now running on professional hosting with a proper database backend!
